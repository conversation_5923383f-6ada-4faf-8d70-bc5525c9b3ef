/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";



::-webkit-scrollbar,
*::-webkit-scrollbar {
  display: none;
} 

@font-face {
  font-family: 'LMicons';
  src: url('./assets/fonts/LMicons.eot');
  src: url('./assets/fonts/LMicons.eot?#iefix') format('embedded-opentype'),
    url('./assets/fonts/LMicons.woff') format('woff'),
    url('./assets/fonts/LMicons.ttf') format('truetype'),
    url('./assets/fonts/LMicons.svg#LMicons') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class*='lm-icon-']:before {
  display: inline-block;
  font-family: 'LMicons';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.lm-icon-delivery:before {
  content: '\004d';
}

.lm-icon-order_tomorrow:before {
  content: '\004e';
}

.lm-icon-sign_out:before {
  content: '\0046';
}

.side-menu{
  float:left;
  width:250px;
  background: linear-gradient(to bottom,#3da735,#c5ee92);
  height:100%;
  border-right:1px solid lightgray;
  position:relative;
}

.main-content{
  float:right;
  width:calc(100% - 250px);
  // border:1px solid red;
  // background-color: yellow;
  height:100%;
  padding:10px;
  overflow-y:auto;
}

.custom-text-color {
  color: #454545 !important;
}

ion-modal.set-pin-modal{
  --width:250px;
  --height:270px;
}

ion-modal.voucher-modal{
  --width: 350px;
  --height: 500px;
}

ion-modal.reject-order-modal{
  --width: 500px;
  --height: 370px;
  --border-radius: 10px;
}

ion-modal.sleep-mode-modal{
  --width: 100%;
  --height: 100%;
  --background: #0f270c;
  opacity: 0.9;
}

app-set-pin{
  border: 1px solid lightgrey;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  background-color: #fff !important;
  -webkit-box-shadow: 0 0 0px 1000px #fff inset !important;
  -webkit-text-fill-color: #000 !important;
}