import { AuthGaurd } from './auth/auth-gaurd.guard';
import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  // {
  //   path: 'home',
  //   loadChildren: () => import('./home/<USER>').then( m => m.HomePageModule),canActivate :[AuthGaurd]
  // },
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full'
  },
  {
    path: 'login',
    loadChildren: () => import('./pages/login/login.module').then(m => m.LoginPageModule)
  },
  {
    path: 'vouchers',
    loadChildren: () => import('./pages/vouchers/vouchers.module').then(m => m.VouchersPageModule)
  },
  {
    path: 'settings',
    loadChildren: () => import('./pages/settings/settings.module').then(m => m.SettingsPageModule)
  },
  {
    path: 'food-menus',
    loadChildren: () => import('./pages/food-menus/food-menus.module').then(m => m.FoodMenusPageModule)
  },
  {
    path: 'food-orders',
    loadChildren: () => import('./pages/food-orders/food-orders.module').then(m => m.FoodOrdersPageModule)
  },
  {
    path: 'set-pin',
    loadChildren: () => import('./modals/set-pin/set-pin.module').then( m => m.SetPinPageModule)
  },
  {
    path: 'voucher',
    loadChildren: () => import('./modals/voucher/voucher.module').then( m => m.VoucherPageModule)
  },
  {
    path: 'reject-order',
    loadChildren: () => import('./modals/reject-order/reject-order.module').then( m => m.RejectOrderPageModule)
  },
  {
    path: 'sleep-mode',
    loadChildren: () => import('./modals/sleep-mode/sleep-mode.module').then( m => m.SleepModePageModule)
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
