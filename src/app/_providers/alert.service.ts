import { Injectable } from '@angular/core';
import { <PERSON>ert<PERSON>ontroller } from '@ionic/angular';
import { EventsService } from './events.service';

@Injectable({
  providedIn: 'root'
})
export class AlertsServices {

  constructor(
    private alertCtrl: AlertController, private events: EventsService
  ) {

  }

  async presentAlert(title, msgText) {
    if (!title) title = 'Lunchmate:';
    return new Promise(async (resolve) => {
      let alert = await this.alertCtrl.create({
        header: title,
        message: msgText,
        buttons: [{
          text: 'OK',
          handler: () => {
            resolve(1);
          }
        }],
        backdropDismiss: false
      });
      await alert.present();
      if (msgText == 'Expired token. Please login again to continue.') {
        this.events.publish("signout:invoked", {});
      }
    });
  }


  presentConfirm(title, message, okBtnText, cancelBtnText): Promise<any> {
    return new Promise(async (resolve, reject) => {
      let confirmPopup = await this.alertCtrl.create({
        header: title,
        message: message,
        backdropDismiss: false,
        cssClass: 'present-confirm-alert',
        buttons: [
          {
            text: cancelBtnText,
            role: 'Cancel',
            cssClass: 'present-confirm-alert-cancel',
            handler: () => {
              reject(null);
            }
          },
          {
            text: okBtnText,
            cssClass: 'present-confirm-alert-ok',
            handler: () => {
              resolve(null);
            }
          }
        ]
      });
      confirmPopup.present();
    });
  }

}
