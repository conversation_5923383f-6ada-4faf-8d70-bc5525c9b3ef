import { Injectable } from '@angular/core';
import { GlobalService } from './global.service';
import { ApisService } from './api.service';
import { LocationHandlersService } from './location-handlers.service';
import { AlertsServices } from './alert.service';
import { LoadingController } from '@ionic/angular';
import { EventsService } from './events.service';

@Injectable({
  providedIn: 'root'
})
export class OrderHandlersService {

  foodOrders: any = [];
  mainOrders: any = [];

  constructor(private globalService: GlobalService, private apisService: ApisService, private locationhandler: LocationHandlersService,
    private alertsServices: AlertsServices, private loadingCtrl: LoadingController, private events: EventsService) { }

  // getOrderType(orderNum, restId){
  //   let orderType = null;
  //   for (let i = 0; i < this.globalService.foodOrders.length; i++) {
  //     const element = this.globalService.foodOrders[i];
  //     if(this.globalService.rest_id == restId && element.order_num == orderNum){
  //       orderType = element.order_type;
  //       break;
  //     }
  //   }
  //   return orderType;
  // }

  getOrderPrimaryId(orderNum) {
    let orderPrimaryId = null;
    let order = this.foodOrders.filter(order => order.order_num == orderNum);
    if (order) {
      orderPrimaryId = order[0].id;
    }
    return orderPrimaryId;
  }

  getOrderType(orderNum) {
    let orderType = null;
    let order = this.foodOrders.filter(order => order.order_num == orderNum);
    if (order) {
      orderType = order[0].order_type;
    }
    return orderType;
  }

  getOrderStatus(orderNum) {
    let orderStatus = null;
    let order = this.foodOrders.filter(order => order.order_num == orderNum);
    if (order) {
      orderStatus = order[0].order_status;
    }
    return orderStatus;
  }

  getFullOrder(orderNum) {
    let fullOrder = null;
    let order = this.mainOrders.filter(order => order.order_num == orderNum);
    if (order) {
      fullOrder = order[0];
    }
    return fullOrder;
  }

  ///
  async markOrderAsPrepared(orderNum, isQr = false, order) {
    console.log({ orderNum });
    if (!this.foodOrders) {
      this.alertsServices.presentAlert(null, 'No order available in the App. Please try again later.');
      return;
    }
    let orderPrimaryId, orderType, orderStatus;
    if (order) {
      orderPrimaryId = order.order_id;
      orderType = order.order_type;
      orderStatus = order.order_status;
    } else {
      orderPrimaryId = this.getOrderPrimaryId(orderNum);
      orderType = this.getOrderType(orderNum);
      orderStatus = this.getOrderStatus(orderNum);
    }

    if ((orderType == 2 || orderType == 4) && orderStatus == 5) {
      this.markOrderAsDelivered(orderNum, isQr);
    }
    else {
      // return;
      // let orderNum = order.order_num;
      // let orderType = order.order_type;
      //
      let processed_message = null;
      if (this.globalService.appSettings.currentDisplayType == 'display') {
        processed_message = this.globalService.processedMessage;
      }
      let loader = await this.loadingCtrl.create({
        message: "Connecting..."
      });
      loader.present();
      // let restId = this.globalService.rest_id;
      let post_data = {
        order_id_primary: orderPrimaryId,
        user_id: this.globalService.assignedRestDetails.rest_admin_id,
        processed_message: processed_message
      };

      this.apisService.markOrderAsPrepared(post_data).subscribe((response: any) => {
        console.log('markOrderAsPrepared ', response);
        loader.dismiss();
        if (response.isSuccess) {
          if (this.globalService.appSettings.currentDisplayType == 'dashboard') {
            this.alertsServices.presentAlert('Order prepared:', 'This order has been marked as prepared!');
          }

          this.events.publish("order:prepared", { orderNum: orderNum, isQr: isQr });

          // let orderNum = arr[1];

          if (this.globalService.appSettings.currentDisplayType == 'dashboard') {
            if ((orderType == 2 || orderType == 4) && this.globalService.appSettings.isTrackingAutomatically) {
              if (this.globalService.trackingStatus == 3) {
                this.alertsServices.presentAlert(null, 'Tracking is active for order number "' + this.globalService.trackedOrderNum + '". Please use "Stop Track" for that order before using "Start Track" for any other order.');
              }
              else {
                this.globalService.trackingStatus = 2;
                // this.locationhandler.fetchLocation(orderNum).then((response) => {
                //   console.log('fetchLocation ', response);
                // },
                //   (err) => {
                //     this.globalService.trackingStatus = 1;
                //     this.alertsServices.presentAlert(null, err);
                //     console.error(err);
                //   })
                //   .catch((excp) => {
                //     this.globalService.trackingStatus = 1;
                //     this.alertsServices.presentAlert(null, excp)
                //     console.error(excp);
                //   });
              }

            }
          }
          else {
            // if (this.globalService.appSettings.isLabelPrintingAutomatically) {
            //   let order = this.getFullOrder(orderNum);
            //   if (order) {
            //     // this.epsonService.connectToLabelPrinter(order);
            //     this.events.publish("printlabel:called", order);
            //   }
            // }
          }
        }
        else {
          this.events.publish("api:cancelled", {});
          this.alertsServices.presentAlert(null, response.resultRes);
        }

      },
        err => {
          this.events.publish("api:cancelled", {});
          console.error(err);
          loader.dismiss();
          this.alertsServices.presentAlert(null, this.apisService.commonNetworkErrorMessage);
        });
    }
  }

  //
  async markOrderAsDelivered(orderNum, isQr = false) {
    console.log({ orderNum });
    if (!this.foodOrders) {
      this.alertsServices.presentAlert(null, 'No order available in the App. Please try again later.');
      return;
    }
    let orderPrimaryId = this.getOrderPrimaryId(orderNum);
    // return;
    let loader = await this.loadingCtrl.create({
      message: "Connecting..."
    });
    loader.present();
    let post_data = {
      order_id_primary: orderPrimaryId,
      user_id: this.globalService.assignedRestDetails.rest_admin_id
    };
    this.apisService.markOrderAsDelivered(post_data).subscribe((response: any) => {
      console.log('markOrderAsDelivered ', response);
      loader.dismiss();
      if (response.isSuccess) {
        this.alertsServices.presentAlert('Order delivered:', 'This order has been marked as delivered!');
        this.events.publish("order:delivered", { orderNum: orderNum, isQr: isQr });
        if (this.globalService.appSettings.currentDisplayType == 'dashboard') {
          // this.locationhandler.stopTrack();
        }
      }
      else {
        this.alertsServices.presentAlert(null, response.resultRes);
      }

    },
      err => {
        console.error(err);
        loader.dismiss();
        this.alertsServices.presentAlert(null, this.apisService.commonNetworkErrorMessage);
      });
  }

  getFormattedDataForLocalPrint(order_item) {
    let itemFreeFromAllergen = this.globalService.getAllergen(order_item.allergen_free_id);
    let allergenAdviceItems = [];
    let allergenAdviceItemsString = "";
    if (order_item.total_allergens.length) {
      for (let i = 0; i < order_item.total_allergens.length; i++) {
        let item = this.globalService.getAllergen(order_item.total_allergens[i]);
        allergenAdviceItems.push(item);
      }
      // console.log("allergenAdviceItems", allergenAdviceItems.join());
    }
    if (allergenAdviceItems.length) {
      allergenAdviceItemsString = allergenAdviceItems.join();
    }
    if (itemFreeFromAllergen) {
      itemFreeFromAllergen = itemFreeFromAllergen.toUpperCase();
    }

    let printData = {
      itemFreeFromAllergen: itemFreeFromAllergen,
      allergenAdvice: allergenAdviceItemsString,
      orderNumber: order_item.order_num,
      customerName: order_item.customer_name
    };

    return printData;
  }

}
