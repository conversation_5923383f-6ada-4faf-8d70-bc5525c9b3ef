import { environment } from '../../environments/environment.prod';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from '@angular/core';
import { timeout, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ApisService {
  commonNetworkErrorMessage: string
  baseUrl: string;
  timeout = 25000;
  headers;

  constructor(public http: HttpClient) {
    this.http = http;
    this.commonNetworkErrorMessage =
      "A network error occurred while communicating with the Lunchmate server. Please check your internet connection and try again.";
    this.baseUrl = environment.API_URL
  }

  initHeaders = function () {
    this.headers = new HttpHeaders();
  };

  setBearer = function (token) {
    this.initHeaders();
    this.headers = this.headers.append("Authorization", "Bearer " + token);
  };

  setBaseUrl(baseUrl) {
    this.baseUrl = baseUrl;
  }

  login(post_data) {
    return this.http
      .post(this.baseUrl + 'loginToDashboard.php', post_data)
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  getVouchersListForDashboard(post_data) {
    return this.http
      .post(this.baseUrl + 'getTokensListForDashboard.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  getRestaurantStatusForDashboard(post_data) {
    return this.http
      .post(this.baseUrl + 'getRestaurantStatusForDashboard.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  markOrderAsPrepared(post_data) {
    return this.http
      .post(this.baseUrl + 'markOrderAsPreparedFromDashboard.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  markOrderAsDelivered(post_data) {
    return this.http
      .post(this.baseUrl + 'markOrderAsDeliveredFromDashboard.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  printTokenFromDashboard(post_data) {
    return this.http
      .post(this.baseUrl + 'printTokenFromDashboard.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  setAllMenuItems(post_data) {
    return this.http
      .post(this.baseUrl + 'setAllMenuItems.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  getMenuItems(post_data) {
    return this.http
      .post(this.baseUrl + 'getMenuItemsForDashboard.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  setPinFromDashboard(post_data) {
    return this.http
      .post(this.baseUrl + 'setPinFromDashboard.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  clearProcessedOrders(post_data) {
    return this.http
      .post(this.baseUrl + 'clearProcessedOrders.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  getOrders(post_data) {
    return this.http
      .post(this.baseUrl + 'getOrdersForDashboard.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  rejectOrder(post_data) {
    return this.http
      .post(this.baseUrl + 'rejectOrderFromDashboard.php', post_data, {
        headers: this.headers
      })
      .pipe(
        timeout(this.timeout),
        map(res => res)
      );
  }

  getRestUsers(post_data) {
    return this.http.post(this.baseUrl + 'getRestUsersForDashboard.php', post_data)
      .pipe(
      timeout(this.timeout),
      map(res => res)
    );
  }

  checkIfLocationUpdated = function(post_data) {
    return this.http
      .post(this.baseUrl + 'checkIfLocationUpdated.php', post_data, {
        headers: this.headers
      })
      .pipe(
      timeout(this.timeout),
      map(res => res)
    );
  };
}
