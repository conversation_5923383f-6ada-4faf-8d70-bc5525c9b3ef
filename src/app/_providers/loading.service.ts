import { Injectable } from '@angular/core';
import { LoadingController } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class LoadingService {

  constructor(
    private loadingCtrl: LoadingController

  ) { }
  async startLoader() {
    await this.loadingCtrl
      .create({
        message: 'Please wait!',
      })
      .then((response) => {
        response.present();
      });
  }
  // Dismiss loader
  dismissLoader() {
    this.loadingCtrl
      .dismiss()
      .then((response) => {
        // console.log('Loader closed!', response);
      })
      .catch((err) => {
        // console.log('Error occured : ', err);
      });
  }
}
