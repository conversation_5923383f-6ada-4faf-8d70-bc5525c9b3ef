import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GlobalService } from './global.service';
import { AlertsServices } from './alert.service';
import { OrderHandlersService } from './order-handlers.service';
import { timeout, map } from 'rxjs/operators';

declare let epson;

@Injectable({
  providedIn: 'root'
})
export class LocalPrinterService {
  http: any;
	constructor(private globalService: GlobalService, http: HttpClient, private alertsServices: AlertsServices, private orderHandlersService: OrderHandlersService) {
		this.http = http;
	}

	// load eposn library
	getEpsonLibLoaded() {
		return new Promise((resolve, reject) => {
			if (typeof epson !== 'undefined') {
				// console.log('Epson pre-loaded', epson);
				resolve('Epson service found');
			} else {
				const script = document.createElement('script');
				script.src = 'assets/vendor/js/epson/epos-print-3.0.0.js';
				document.body.appendChild(script);

				const interval = setInterval(() => {
					if (typeof epson !== 'undefined') {
						clearInterval(interval);
						// console.log('Epson newly-loaded');
						resolve('Epson service found');
					}
				}, 500);
				setTimeout(() => {
					clearInterval(interval);
					reject(
						'Could not load printing helper files. Please try again later.'
					);
				}, 10000);
			}
		});
	}

	getVoucherDataForPrint(voucher, voucherContext) {
		return new Promise((resolve, reject) => {
			//  console.log('res  - ', res);
			const builder = new epson.ePOSBuilder();
			const verticalHeight = 20;
			builder.addTextStyle(0, 0, 1);
			builder.addTextAlign(builder.ALIGN_CENTER);
			builder.addTextDouble(true, true).addText('LunchMate Voucher \n\r').addTextDouble(false, false);
			builder.addFeedUnit(verticalHeight);
			// builder.addTextAlign(builder.ALIGN_LEFT);
			builder.addText('Scan this code using LunchMate app \n\r');
			builder.addText('to receive \n\r');
			// builder.addText('Scan this code using LunchMate app to receive');
			// builder.addTextAlign(builder.ALIGN_CENTER);
			builder.addTextDouble(true, true).addText('£' + voucher.amount).addTextDouble(false, false);
			builder.addText('\n\r off your next meal \n\r');
			builder.addFeedUnit(verticalHeight);
			// const qr_string = tokenCode;
			// console.log(qr_string);
			// builder.addSymbol(
			//   qr_string,
			//   builder.SYMBOL_QRCODE_MODEL_2,
			//   builder.LEVEL_DEFAULT,
			//   8,
			//   4,
			//   0
			// );

			//  let topLogoContext = res[0];
			//     let topLogocanvasHeight = res[1];

			// let builder = new epson.ePOSBuilder();
			// builder.brightness = "1.0";
			// builder.halftone = builder["HALFTONE_THRESHOLD"];

			builder.addTextAlign(builder.ALIGN_CENTER);
			//QR code
			builder.addImage(voucherContext, 0, 0, 300, 300);
			builder.addFeedUnit(verticalHeight);
			builder.addTextDouble(true, true).addText(voucher.token_code + ' \n\r').addTextDouble(false, false);
			builder.addFeedUnit(verticalHeight);
			builder.addText(this.getPhonetic(voucher.token_code));
			builder.addFeedUnit(verticalHeight * 4);
			builder.addCut(builder.CUT_FEED);
			resolve(builder);
		});
	}
	//
	getPhonetic(tokenCode) {
		let phoneticArray = {
			'1': 'one', '2': 'two', '3': 'three', '4': 'four', '5': 'five', '6': 'six', '7': 'seven', '8': 'eight', '9': 'nine', '0': 'zero',
			'A': 'alfa', 'B': 'bravo', 'C': 'charlie', 'D': 'delta', 'E': 'echo', 'F': 'foxtrot', 'G': 'golf', 'H': 'hotel',
			'I': 'india', 'J': 'juliett', 'K': 'kilo', 'L': 'lima', 'M': 'mike', 'N': 'november', 'O': 'oscar', 'P': 'papa', 'Q': 'quebec',
			'R': 'romeo', 'S': 'sierra', 'T': 'tango', 'U': 'uniform', 'V': 'victor', 'W': 'whiskey', 'X': 'xray', 'Y': 'yankee', 'Z': 'zulu'
		};
		let phoneticString = '';
		for (let i = 0; i < tokenCode.length; i++) {
			phoneticString += phoneticArray[tokenCode[i]] + ' ';
		}
		return phoneticString;
	}

	connectToEpsonLabelPrinter(order_item) {
		this.getEpsonLibLoaded().then(
			response => {
				console.log("eposPrintLib loaded.");
				//  let labelType = Math.floor((Math.random() * 4) + 1);
				let data = this.getEpsonBuilderString(order_item);
				// console.log("printData ", data);
				let receipt_xml = data.toString();
				//  console.log('receipt_xml     ---', receipt_xml);
				this.printLabelLocallyFromEpson(receipt_xml);
			},
			printLoadRejected => {
				console.error("printLoadRejected ", printLoadRejected);
			}
		);
	}

	getEpsonBuilderString(order) {
		let printData = this.orderHandlersService.getFormattedDataForLocalPrint(order);
		// console.log('printData ', printData);
		let builder = new epson.ePOSBuilder();

		builder.addTextStyle(1, 0, 1);
		if (printData.itemFreeFromAllergen) {
			builder.addTextAlign(builder.ALIGN_CENTER);
			builder
				.addTextDouble(true, true)
				.addText(" " + printData.itemFreeFromAllergen + " FREE" + " " + "\n\r")
				.addTextDouble(false, false);
		}
		if (printData.allergenAdvice) {
			builder.addTextStyle(0, 0, 1);
			builder.addFeedUnit(20);
			builder.addTextFont(builder.FONT_B);
			builder.addTextAlign(builder.ALIGN_LEFT);
			builder
				.addText("Allergen advice - ")
				.addTextStyle(0, 0, 0)
				.addText(
					"this product contains following allergens: " +
					printData.allergenAdvice +
					"\n\r"
				);
			builder.addTextFont(builder.FONT_A);
		}

		builder.addTextAlign(builder.ALIGN_CENTER);
		builder.addTextStyle(0, 0, 1);
		builder.addFeedUnit(10);
		builder.addText(printData.customerName + "\n\r");
		builder.addTextStyle(1, 0, 1);
		builder.addFeedUnit(10);
		builder
			.addTextDouble(true, true)
			.addText(" " + "#" + printData.orderNumber + " " + "\n\r")
			.addTextDouble(false, false);
		builder.addTextStyle(0, 0, 0);
		builder.addFeedUnit(50);
		builder.addCut(builder.CUT_FEED);
		return builder;
	}

	printLabelLocallyFromEpson(xmlString) {
		let connection_timeout = 10000;
		let printerAddress =
			"http://" +
			this.globalService.availablePrinters[this.globalService.appSettings.selectedPrinterIndex].printerIpAddress +
			"/cgi-bin/epos/service.cgi?timeout=" +
			connection_timeout +
			"&devid=" +
			this.globalService.availablePrinters[this.globalService.appSettings.selectedPrinterIndex].printerLabel;
		// console.log(printerAddress);
		//"http://*************/cgi-bin/epos/service.cgi?timeout=10000&devid=local_printer";
		let that = this;
		let epos = new epson.ePOSPrint();
		epos.onreceive = function (res) {
			// console.log("res onreceive === >", res);
		};

		epos.onerror = function (err) {
			console.error("err onerror === >", err);
			that.alertsServices.presentAlert(
				"Printer Not Found",
				'Could not connect to local label printer. Please make sure that printer is switched on and configured properly in "Settings" dialog box.'
			);
		};

		epos.send(printerAddress, xmlString);

	}

	connectToHoneywellLabelPrinter(order) {
		const post_data = this.createHonewellJsonData(order);
		console.log(post_data);
		this.printLabelLocallyFromHoneywell(post_data).subscribe(
			response => {
				console.log('printLabelLocallyFromEpson ', response);
			},
			err => {
				console.error(err);
				this.alertsServices.presentAlert(
					"Printer Not Found",
					'Could not connect to local label printer. Please make sure that printer is switched on and configured properly in "Settings" dialog box.'
				);
			}
		);
	}

	createHonewellJsonData(order) {
		// console.log(order);
		// let json = {"allergen_free": "NUT FREE", "allergen_list": "eggs, gluten", "order_num": "9355", "name": "Dave Smith", "name_allergens": "Dave Smith"};
		let printData = this.orderHandlersService.getFormattedDataForLocalPrint(order);
		// console.log(printData);
		let json = { "allergen_free": printData.itemFreeFromAllergen ? printData.itemFreeFromAllergen + ' FREE' : '', "allergen_list": printData.allergenAdvice ? printData.allergenAdvice : '', "order_num": printData.orderNumber, "name": printData.allergenAdvice ? '' : printData.customerName, "name_allergens": printData.allergenAdvice ? printData.customerName : '' };
		return json;
	}

	printLabelLocallyFromHoneywell(post_data) {
		return this.http
			.post('http://' + this.globalService.availablePrinters[this.globalService.appSettings.selectedPrinterIndex].printerIpAddress + ':5000/order_label', post_data, {
				// headers: this.headers
			})
			.pipe(
				timeout(10000),
				map(res => res)
			);
	}

	checkPrinterStatus(printerIndex, printerIpAddress) {
		let pingIpAddress;
		if (printerIndex == 0) {
			pingIpAddress = 'http://' + printerIpAddress + '/cgi-bin/epos/service.cgi';
		}
		else if (printerIndex == 1) {
			pingIpAddress = 'http://' + printerIpAddress + ':5000/ping';
		}
		return this.http
			.get(pingIpAddress, {
				// headers: this.headers
			})
			.pipe(
				timeout(900),
				map(res => res)
			);
	}
}
