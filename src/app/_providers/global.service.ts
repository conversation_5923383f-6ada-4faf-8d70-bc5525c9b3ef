import { Injectable } from '@angular/core';
import { EventsService } from './events.service';
import { Storage } from '@ionic/storage';
import { ModalController, NavController } from '@ionic/angular';
import { SleepModePage } from '../modals/sleep-mode/sleep-mode.page';
import { Idle, DEFAULT_INTERRUPTSOURCES } from '@ng-idle/core';
import { Capacitor } from '@capacitor/core';
import { App } from '@capacitor/app';
import { AlertsServices } from './alert.service';
import { Location } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class GlobalService {
  public userName: any;
  user_full_name: any;
  public rest_id: any;
  public rest_details: any;
  assignedRestDetails : any;
  public user_id: any;
  user_email: any = null;
  user_first_name: any = null;


  userDetails = { username: null, password: null };
  appSettings = {
    currentDisplayType: 'display',
    isTrackingAutomatically: false,
    // isLabelPrintingAutomatically: false,
    selectedPrinterIndex: 0,
    displayIdleScreen: true,
    numberOfMinuteOfInactivity: 30,
    clearProcessedAfterMinutes: 30,
  };
  availablePrinters: any = [
    {
      printerType: 1,
      printerName: 'EPSON TMi printer',
      printerIpAddress: '*************',
      printerLabel: 'local_printer'
    },
    {
      printerType: 2,
      printerName: 'Honeywell label printer',
      printerIpAddress: '*************',
      printerLabel: null
    }
  ];
  todaysTotalAmount = 0;
  currentPage = null;
  access_token = null;
  allLoadingDone = false;
  processedMessage = "We wish to inform you that your order has been prepared.";
  trackingStatus = 1;
  trackedOrderNum = null;
  idleState = 'Not started.';
  timedOut = false;

  constructor(private events: EventsService, private storage: Storage, private modalCtrl: ModalController, private idle: Idle, private _location: Location, private alertsService: AlertsServices, private navCtrl: NavController) {
    this.events.subscribe('show:sleep:screen', data => {
      this.showSleepMode();
    });
    this.userName = '';
    this.user_full_name = null;
    this.rest_id = '';
    this.rest_details = null;
    if (Capacitor.isNativePlatform()) {
      setTimeout(() => {
        this.proceedWithDeviceFeatures();
      }, 0);
    }
  }

  getAllergenList() {
    let allergenImg = [
      {
        id: 1,
        name: "Eggs",
        labelName: "Egg",
        description:
          "Eggs are often found in cakes, some meat products, mayonnaise, mousses, pasta, quiche, sauces and pastries or foods brushed or glazed with egg.",
        img: "eggs.png"
      },
      {
        id: 2,
        name: "Milk",
        labelName: "Milk",
        description:
          "Milk is a common ingredient in butter, cheese, cream, milk powders and yoghurt. It can also be found in foods brushed or glazed with milk, and in powdered soups and sauces.",
        img: "lactose.png"
      },
      {
        id: 3,
        name: "Fish",
        labelName: "Fish",
        description:
          "You will find this in some fish sauces, pizzas, relishes, salad dressings, stock cubes and Worcestershire sauce.",
        img: "fish.png"
      },
      {
        id: 4,
        name: "Crustaceans(e.g. lobster, prawns)",
        labelName: "Crustacean",
        description:
          "Crabs, lobster, prawns and scampi are crustaceans. Shrimp paste, often used in Thai and south-east Asian curries or salads, is an ingredient to look out for.",
        img: "crustaceans.png"
      },
      {
        id: 5,
        name: "Molluscs(e.g. oysters)",
        labelName: "Mollusc",
        description:
          "These include mussels, land snails, squid and whelks, but can also be commonly found in oyster sauce or as an ingredient in fish stews.",
        img: "molluscs.png"
      },
      {
        id: 6,
        name: "Peanuts",
        labelName: "Peanut",
        description:
          "Peanuts are actually a legume and grow underground, which is why it's sometimes called a groundnut. Peanuts are often used as an ingredient in biscuits, cakes, curries, desserts, sauces (such as satay sauce), as well as in groundnut oil and peanut flour.",
        img: "peanuts.png"
      },
      {
        id: 7,
        name:
          "All tree nuts(almonds, hazelnuts, walnuts, cashews, pecans, brazils, pistachios, macadamia nuts and Queensland nuts)",
        labelName: "Tree Nut",
        description:
          "Not to be mistaken with peanuts (which are actually a legume and grow underground), this ingredient refers to nuts which grow on trees, like cashew nuts, almonds and hazelnuts. You can find nuts in breads, biscuits, crackers, desserts, nut powders (often used in Asian curries), stir-fried dishes, ice cream, marzipan (almond paste), nut oils and sauces.",
        img: "nuts.png"
      },
      {
        id: 8,
        name: "Sesame seeds",
        labelName: "Sesame Seed",
        description:
          "These seeds can often be found in bread (sprinkled on hamburger buns for example), breadsticks, houmous, sesame oil and tahini. They are sometimes toasted and used in salads.",
        img: "sesame_seeds.png"
      },
      {
        id: 9,
        name: " Cerelas containing gluten",
        labelName: "Gluten",
        description:
          "Wheat (such as spelt and Khorasan wheat/Kamut), rye, barley and oats is often found in foods containing flour, such as some types of baking powder, batter, breadcrumbs, bread, cakes, couscous, meat products, pasta, pastry, sauces, soups and fried foods which are dusted with flour.",
        img: "gluten.png"
      },
      {
        id: 10,
        name: "Soya",
        labelName: "Soya",
        description:
          "Often found in bean curd, edamame beans, miso paste, textured soya protein, soya flour or tofu, soya is a staple ingredient in oriental food. It can also be found in desserts, ice cream, meat products, sauces and vegetarian products.",
        img: "soya.png"
      },
      {
        id: 11,
        name: "Celery and celeriac",
        labelName: "Celery",
        description:
          "This includes celery stalks, leaves, seeds and the root called celeriac. You can find celery in celery salt, salads, some meat products, soups and stock cubes.",
        img: "celery.png"
      },
      {
        id: 12,
        name: "Mustard",
        labelName: "Mustard",
        description:
          "Liquid mustard, mustard powder and mustard seeds fall into this category. This ingredient can also be found in breads, curries, marinades, meat products, salad dressings, sauces and soups.",
        img: "mustard.png"
      },
      {
        id: 13,
        name: "Lupin",
        labelName: "Lupin",
        description:
          "Yes, lupin is a flower, but it's also found in flour! Lupin flour and seeds can be used in some types of bread, pastries and even in pasta.",
        img: "lupin.png"
      },
      {
        id: 14,
        name: "Sulphur dioxide and sulphites",
        labelName: "Sulphur",
        description:
          "This is an ingredient often used in dried fruit such as raisins, dried apricots and prunes. You might also find it in meat products, soft drinks, vegetables as well as in wine and beer. If you have asthma, you have a higher risk of developing a reaction to sulphur dioxide.",
        img: "sulphur_dioxide.png"
      }
    ];
    return allergenImg;
  }

  getAllergen(id) {
    let allergenList = this.getAllergenList();
    let index = allergenList.findIndex(allergen => id == allergen.id);
    if (index != -1) {
      //  console.log(index, imgPath + allergenList[index].img);
      return allergenList[index].labelName;
    } else {
      return null;
    }
  }

  configSleepMode() {
    console.log('configSleepMode');
    // sets the default interrupts, in this case, things like clicks, scrolls, touches to the document
    this.idle.setInterrupts(DEFAULT_INTERRUPTSOURCES);
    this.idle.onIdleEnd.subscribe(() => this.idleState = 'No longer idle.');
    this.idle.onTimeout.subscribe(() => {
      this.idleState = 'Timed out!';
      console.log(this.idleState);
      this.timedOut = true;
      this.events.publish('show:sleep:screen', {});
    });

    this.idle.onIdleStart.subscribe(() => {
      this.idleState = 'You\'ve gone idle!';
      console.log(this.idleState);
    });

    // this.idle.onTimeoutWarning.subscribe((countdown) => { this.idleState = 'You will time out in ' + countdown + ' seconds!'; console.log(this.idleState); });
  }

  setSleepMode() {
    console.log('setSleepMode');
    // sets an idle timeout
    let isSecs = false; // make true for sleep timeout in secs, false for mins
    let coEff;
    if (isSecs) {
      coEff = 0.5;
    } else {
      coEff = 30;
    }
    this.idle.setIdle(this.appSettings.numberOfMinuteOfInactivity * coEff);
    // sets a timeout period
    this.idle.setTimeout(this.appSettings.numberOfMinuteOfInactivity * coEff);

    if (this.appSettings.displayIdleScreen) {
      this.resetSleepMode();
    }
    else {
      this.stopSleepMode();
    }
  }

  resetSleepMode() {
    this.idle.watch();
    this.idleState = 'Started.';
    console.log(this.idleState);
    this.timedOut = false;
  }

  stopSleepMode() {
    this.idle.stop();
  }

  async showSleepMode() {
    let modal = await this.modalCtrl.create({
      component: SleepModePage,
      componentProps: {
        user_full_name: this.userDetails.username
      },
      showBackdrop: false,
      backdropDismiss: true,
      cssClass: "sleep-mode-modal"
    });
    modal.present();
    modal.onDidDismiss().then(async res => {
      this.resetSleepMode();
    });
  }

  getSettingsFromStorage() {
    this.storage.get('appSettingsPos').then(appSettingsStorage => {
      console.log({ appSettingsStorage });
      if (appSettingsStorage) {
        this.appSettings = appSettingsStorage;
      }
    });
  }

  saveSettingsToStorage() {
    this.storage.set("appSettingsPos", this.appSettings);
    this.setSleepMode();
  }

  proceedWithDeviceFeatures() {
    // SplashScreen.hide();
    // StatusBar.show();
    // StatusBar.setOverlaysWebView({ overlay: false });
    // StatusBar.setStyle({ style: Style.Dark });
    //
    App.addListener('backButton', ({ canGoBack }) => {
      // console.log({ canGoBack });
      if (this._location.isCurrentPathEqualTo('/') || this._location.isCurrentPathEqualTo('/login') || this._location.isCurrentPathEqualTo('/food-orders')) {
        this.alertsService.presentConfirm('Dashboard:', 'Are you sure you want to quit the application?', 'Ok', 'Cancel').then(() => {
          App.exitApp();
        });
      } else {
        // window.history.back();
        // this._location.back();
        this.navCtrl.navigateRoot(['food-orders']);
      }
    });
  }

}
