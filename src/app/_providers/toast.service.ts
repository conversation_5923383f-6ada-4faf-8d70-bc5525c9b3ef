import { Injectable } from '@angular/core';
import { ToastController } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class ToastService {

  constructor(
    public toastController: ToastController,

  ) { }
  async toastServices(msg, color, pos) {
    const toast = await this.toastController.create({
      message: msg,
      duration: 2000,
      position: pos,
      color: color,
    });
    toast.present();
  }

}
