<ion-header>
  <ion-toolbar color="primary">
    <ion-title class="title-left">Lunchmate Dashboard - Food orders</ion-title>
    <div class="title-right ion-float-right ion-text-right">
      <div>{{MOD.length}} Order(s) to prepare</div>
      <div>{{POD.length + ROD.length}} Order(s) prepared</div>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="side-menu">
    <app-side-menu></app-side-menu>
  </div>
  <div class="main-content">
    <ion-row>
      <ion-col style="padding-top:0px;">
        <ion-toolbar>
          <ion-title>Orders to prepare</ion-title>
        </ion-toolbar>
      </ion-col>
      <ion-col style="padding-top:0px;">
        <ion-toolbar>
          <ion-title>Processed orders</ion-title>
        </ion-toolbar>
      </ion-col>
    </ion-row>

    <ion-row [style.height]="'calc(100% - 56px)'">
      <ion-col style="height:100%;overflow-y:auto;">
        <ion-card [@popItemFromMainTrigger]="order.animationState"
          (@popItemFromMainTrigger.start)="popItemFromMainTriggerStart($event)"
          (@popItemFromMainTrigger.done)="popItemFromMainTriggerDone($event)" *ngFor="let order of MOD; let i=index"
          class="ion-text-wrap">
          <ion-card-header (click)="orderHeaderClicked(i, 'main')"
            [ngClass]="{'expanded-main-order-heading': isOrderExpanded(i, 'main')}">
            <div class="left-item white-space-normal custom-text ion-float-left">
              <div> {{order.order_num}} - {{order.customer_name}}</div>
            </div>
            <div class="ion-float-right">
              <img *ngIf="order.order_type == 1 || (order.order_type == 3 && order.order_time_remaining_status > 2)"
                class="lunchmate-icon" src="{{lunchmateIcon}}">
              <img *ngIf="order.order_type == 7" class="lunchmate-icon" src="{{lunchposIcon}}">
              <i *ngIf="(order.order_type == 4 || order.order_type == 3) && (order.order_time_remaining_status <= 2)"
                class="lm-icon-order_tomorrow" [ngClass]="{'shaky': order.order_time_remaining_status == 2}"></i>
              <i class="lm-icon-delivery" *ngIf="order.order_type == 2 || order.order_type == 4"></i>
            </div>
          </ion-card-header>
          <ion-card-content *ngIf="isOrderExpanded(i, 'main')" class="ion-padding">

            <div *ngIf="order.order_status==3" class="rejected-order">This order has been rejected</div>
            <div *ngIf="order.order_status==4 || order.order_status==5" class="accepted-order">This order has been
              processed</div>
            <div *ngIf="globalService.trackedOrderNum==order.order_num && globalService.trackingStatus==3"
              class="pulsating-circle"></div>
            <div *ngIf="order.order_status==6" class="accepted-order">This order has been delivered</div>

            <div *ngFor="let order_item of order.items" class="order-item">
              <ion-row class="custom-text-color">
                <ion-col size="7">
                  <div><strong>{{order_item.item_name}}</strong></div>
                  <div *ngIf="order_item.options_string" style="margin-left:5px;">Options:
                    {{order_item.options_string}}
                  </div>
                  <div *ngIf="order_item.additions_string" style="margin-left:5px;">Additions:
                    {{order_item.additions_string}}</div>
                </ion-col>
                <ion-col>

                  <div>
                    <ion-button button expand="block" size="small" color="tertiary" fill="outline"
                      (click)="printLabelClicked(order, order_item)">
                      Print Label
                    </ion-button>
                  </div>
                  </ion-col>
                  </ion-row>

                  </div>

                  <div class="order-type-text custom-text-color ">Order Type: {{order.order_type_text}}
                  </div>
                  <div class="custom-text-color">Order Time: {{order.order_date}}</div>

                  <div class="custom-text-color" *ngIf="order.order_type == 1">Order Pickup Time: ASAP</div>
                  <div class="custom-text-color" *ngIf="order.order_type == 2">Order Delivery Time: ASAP</div>
                  <div class="custom-text-color" *ngIf="order.order_type == 3">Order Pickup Time:
                    {{order.order_acquire_datetime}}</div>
                  <div class="custom-text-color" *ngIf="order.order_type == 4">Order Delivery Time:
                    {{order.order_acquire_datetime}}</div>

                  <div class="custom-text-color">Customer name: {{order.customer_name}}</div>

                  <ion-button button fill="clear" size="small" color="tertiary" (click)="callClicked($event, order)">
                    <ion-icon slot="start" name="call"></ion-icon>Call customer
                  </ion-button>

                  <!-- these buttons will not appear if order rejected -->
                  <ion-row *ngIf="order.order_status !=3 ">
                    <ion-col>
                      <ion-button button *ngIf="order.order_type == 1 || order.order_type == 3 || order.order_status==2"
                        [disabled]="(order.order_status > 3 || order.can_be_rejected == 0)" expand="block" size="small"
                        color="reject" (click)="rejectOrderClicked(order)">
                        <ion-icon slot="start" name="sad"></ion-icon>Reject order
                      </ion-button>
                      <!-- color="{{globalService.trackedOrderNum==order.order_num && globalService.trackingStatus==3 ? 'reject' : 'lunchmate-green'}}" -->
                      <ion-button button
                        *ngIf="(order.order_type == 2 || order.order_type == 4) && order.order_status==5"
                        [disabled]="globalService.trackingStatus==2" expand="block" size="small"
                        (click)="trackOrderClicked(order)">
                        <ion-icon slot="start"
                          name="{{globalService.trackedOrderNum==order.order_num && globalService.trackingStatus==3 ? 'hand' : 'locate'}}">
                        </ion-icon>
                        {{globalService.trackedOrderNum==order.order_num && globalService.trackingStatus==3 ? 'Stop' : 'Start'}}
                        Track
                      </ion-button>
                    </ion-col>
                    <ion-col>
                      <ion-button button *ngIf="order.order_status==2" expand="block" size="small"
                        color="lunchmate-green"
                        (click)="orderProcessedClicked(order)">
                        Processed
                        <ion-icon slot="end" name="happy"></ion-icon>
                        </ion-button>
                        <ion-button button
                          *ngIf="order.order_status==5 && (order.order_type == 2 || order.order_type == 4)"
                          expand="block" size="small" color="lunchmate-green"
                        (click)="orderDeliveredClicked(order)">
                        Delivered
                        <ion-icon slot="end" name="thumbs-up"></ion-icon>
                        </ion-button>
                    </ion-col>
                  </ion-row>
                  <ion-row *ngIf="(order.order_status==1 || order.order_status==2) && order.can_be_rejected==0"
                    class="rejected-order">This
                    order can not be rejected</ion-row>
                  </ion-card-content>
                  </ion-card>
                  <ion-row *ngIf="!MOD.length" class="custom-text-color ion-margin-left ion-margin-top">
                    <ion-col>
                      <ion-card class="ion-padding">
                        No orders to display here!
                      </ion-card>
                    </ion-col>
                  </ion-row>
                  </ion-col>

                  <ion-col style="height:100%;">
                    <div class="prepared-order-list"
                      [ngStyle]="{'height':'calc(100% - ' + rejectedOrderHeight + 'px)'}">
                      <ion-row>
                        <ion-col style="height:100%;overflow-y:auto;">
                        <ion-card [@pushItemInProcessedTrigger]="order.animationState"
                          (@pushItemInProcessedTrigger.start)="pushItemInProcessedTriggerStart($event)"
                          (@pushItemInProcessedTrigger.done)="pushItemInProcessedTriggerDone($event)"
                          *ngFor="let order of POD; let i=index" class="ion-text-wrap">
                          <ion-card-header (click)="orderHeaderClicked(i, 'processed')"
                            class="processed-order-heading custom-text-color"
                            [ngClass]="{'expanded-processed-order-heading': isOrderExpanded(i, 'processed')}">
                            <div class="left-item white-space-normal custom-text-color ion-float-left">
                              <div> {{order.order_num}} - {{order.customer_name}}</div>
                            </div>
                            <div class="lm-icons ion-float-right">
                              <img *ngIf="order.order_type == 1" class="lunchmate-icon" src="{{lunchmateIconWhite}}">
                              <img *ngIf="order.order_type == 7" class="lunchmate-icon" src="{{lunchposIconWhite}}">
                              <i *ngIf="order.order_type == 4 || order.order_type == 3"
                                class="lm-icon-order_tomorrow"></i>
                              <i class="lm-icon-delivery" *ngIf="order.order_type == 2 || order.order_type == 4"></i>
                            </div>
                          </ion-card-header>

                          <ion-card-content *ngIf="isOrderExpanded(i, 'processed')" class="ion-padding">



                            <div *ngFor="let order_item of order.items" class="order-item">
                              <ion-row class="custom-text-color">
                                <ion-col size="7">
                                  <div><strong>{{order_item.item_name}}</strong></div>
                                  <div *ngIf="order_item.options_string" style="margin-left:5px;">Options:
                                    {{order_item.options_string}}</div>
                                  <div *ngIf="order_item.additions_string" style="margin-left:5px;">Additions:
                                    {{order_item.additions_string}}</div>
                                </ion-col>
                                <ion-col>
                                  <div>
                                    <ion-button button expand="block" size="small" color="tertiary" fill="outline"
                                      (click)="printLabelClicked(order, order_item)">
                                      Print Label
                                    </ion-button>
                                  </div>
                                </ion-col>
                              </ion-row>



                            </div>

                            <div class="order-type-text custom-text-color">Order Type: {{order.order_type_text}}
                            </div>

                            <div class="custom-text-color">Order Time: {{order.order_date}}</div>

                            <div class="custom-text-color" *ngIf="order.order_type == 1">Order Pickup Time: ASAP</div>
                            <div class="custom-text-color" *ngIf="order.order_type == 2">Order Delivery Time: ASAP</div>
                            <div class="custom-text-color" *ngIf="order.order_type == 3">Order Pickup Time:
                              {{order.order_acquire_datetime}}</div>
                            <div class="custom-text-color" *ngIf="order.order_type == 4">Order Delivery Time:
                              {{order.order_acquire_datetime}}</div>

                            <div class="custom-text-color">Customer name: {{order.customer_name}}</div>
                            <ion-button button color="tertiary" fill="clear" (click)="callClicked($event, order)">
                              <ion-icon slot="start" name="call"></ion-icon>Call customer
                            </ion-button>
                          </ion-card-content>
                        </ion-card>
                         <ion-row *ngIf="!POD.length" class="custom-text-color ion-margin-left" style="margin-top:8px;">
                           <ion-col>
                             <ion-card class="ion-padding">
                               No orders to display here!
                             </ion-card>
                           </ion-col>
                         </ion-row>
                      </ion-col>
                      </ion-row>
                      <!-- <ion-row *ngIf="!processedOrderData.length" margin-left margin-top>
                No items to display.
              </ion-row> -->
                    </div>

                    <!-- | slice:0:1  -->
                    <div class="rejected-order-list" #rejetedOrderList *ngIf="ROD.length">
                      <ion-toolbar>
                        <ion-title>Rejected Orders:</ion-title>
                      </ion-toolbar>
                      <ion-row>
                        <ion-col style="height:100%;overflow-y:auto;">
                        <ion-card [@pushItemInRejectedTrigger]="order.animationState"
                          (@pushItemInRejectedTrigger.start)="pushItemInRejectedTriggerStart($event)"
                          (@pushItemInRejectedTrigger.done)="pushItemInRejectedTriggerDone($event)"
                          *ngFor="let order of ROD; let i=index" class="ion-text-wrap">
                          <ion-card-header (click)="orderHeaderClicked(i, 'rejected')" class="rejected-order-heading"
                            [ngClass]="{'expanded-rejected-order-heading': isOrderExpanded(i, 'rejected')}">
                            <div class="left-item white-space-normal ion-float-left">
                              <div> {{order.order_num}} - {{order.customer_name}}</div>
                            </div>
                            <div class="lm-icons ion-float-right">
                              <img *ngIf="order.order_type == 1" class="lunchmate-icon" src="{{lunchmateIconWhite}}">
                              <i *ngIf="order.order_type == 4 || order.order_type == 3"
                                class="lm-icon-order_tomorrow"></i>
                              <i class="lm-icon-delivery" *ngIf="order.order_type == 2 || order.order_type == 4"></i>
                            </div>
                          </ion-card-header>

                          <ion-card-content *ngIf="isOrderExpanded(i, 'rejected')" class="ion-padding">


                            <div *ngFor="let order_item of order.items" class="order-item">
                              <ion-row class="custom-text-color padding-top-0">
                                <ion-col size="7">
                                  <div><strong>{{order_item.item_name}}</strong></div>
                                  <div *ngIf="order_item.options_string" style="margin-left:5px;">Options:
                                    {{order_item.options_string}}</div>
                                  <div *ngIf="order_item.additions_string" style="margin-left:5px;">Additions:
                                    {{order_item.additions_string}}</div>
                                </ion-col>
                                <ion-col>
                                  <div>
                                    <ion-button button expand="block" size="small" color="tertiary" fill="outline"
                                      (click)="printLabelClicked(order, order_item)">
                                      Print Label
                                    </ion-button>
                                  </div>
                                </ion-col>
                              </ion-row>


                            </div>

                            <div class="order-type-text custom-text-color">Order Type: {{order.order_type_text}}
                            </div>

                            <div class="custom-text-color">Order Time: {{order.order_date}}</div>

                            <div class="custom-text-color" *ngIf="order.order_type == 1">Order Pickup Time: ASAP</div>
                            <div class="custom-text-color" *ngIf="order.order_type == 2">Order Delivery Time: ASAP</div>
                            <div class="custom-text-color" *ngIf="order.order_type == 3">Order Pickup Time:
                              {{order.order_acquire_datetime}}</div>
                            <div class="custom-text-color" *ngIf="order.order_type == 4">Order Delivery Time:
                              {{order.order_acquire_datetime}}</div>

                            <div class="custom-text-color">Customer name: {{order.customer_name}}</div>
                            <ion-button button color="tertiary" fill="clear" (click)="callClicked($event, order)">
                              <ion-icon slot="start" name="call"></ion-icon>Call customer
                            </ion-button>
                          </ion-card-content>
                        </ion-card>
                      </ion-col>
                      </ion-row>
                      <!-- <ion-row *ngIf="!rejectedOrders.length">
                No items to display.
              </ion-row> -->
                    </div>
                  </ion-col>
                  </ion-row>
  </div>
</ion-content>
