import { <PERSON>mpo<PERSON>, <PERSON>ementR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>roller, ModalController, NavController } from '@ionic/angular';
import { ApisService } from 'src/app/_providers/api.service';
import { GlobalService } from 'src/app/_providers/global.service';
import { OrderHandlersService } from 'src/app/_providers/order-handlers.service';
import { RejectOrderPage } from 'src/app/modals/reject-order/reject-order.page';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { AlertsServices } from 'src/app/_providers/alert.service';
import { LocalPrinterService } from 'src/app/_providers/local-printer.service';
import { EventsService } from 'src/app/_providers/events.service';

interface OrderData {
  order_id: any,
  order_num: any,
  order_acquire_datetime: any,
  order_date: any,
  order_status: any,
  can_be_rejected: any,
  // item_num: any,
  item_name: any,
  item_price: any,
  total_allergens: any,
  allergen_free_id: any,
  additions_string: any,
  options_string: any,
  customer_name: any,
  customer_phone_number: any,
  order_type_text: any,
  order_type: any,
  is_cleared_for_pos: any,
  animationState: any
}

interface ItemData {
  item_name: any,
  item_price: any,
  total_allergens: any,
  allergen_free_id: any,
  additions_string: any,
  options_string: any,
}

interface OD {
  order_id: any,
  order_num: any,
  order_acquire_datetime: any,
  order_date: any,
  order_status: any,
  can_be_rejected: any,
  items: any,
  customer_name: any,
  customer_phone_number: any,
  order_type_text: any,
  order_type: any,
  is_cleared_for_pos: any,
  animationState: any,
  order_time_remaining_status: any
}

@Component({
  selector: 'app-food-orders',
  templateUrl: './food-orders.page.html',
  styleUrls: ['./food-orders.page.scss'],
  animations: [
    trigger('popItemFromMainTrigger', [
      state('processed', style({ transform: 'translateX(120%) translateY(-300%)' })),
      state('rejected', style({ transform: 'translateX(120%) translateY(50%)' })),
      transition('* => processed', [
        animate('300ms ease-out')
      ]),
      transition('* => rejected', [
        animate('300ms ease-out')
      ]),
      transition('void => *', [
        // animate('200ms ease-out', style({ transform: 'translateX(-100%)' }))
      ]),
      transition('* => void', [
        // animate('3000ms ease-in', style({ transform: 'translateX(100%) translateY(-200%)' }))
      ])
    ]),
    trigger('pushItemInProcessedTrigger', [
      transition('void => processed', [
        style({ transform: 'translateX(-100%) translateY(-200%)' }),
        animate('200ms ease-out')
      ])
    ]),
    trigger('pushItemInRejectedTrigger', [
      transition('void => rejected', [
        style({ transform: 'translateX(-100%) translateY(-200%)' }),
        animate('200ms ease-out')
      ])
    ])
  ]
})
export class FoodOrdersPage implements OnInit {

  // @ViewChild("rejetedOrderList") elemRejectedOrder: ElementRef;
  private elemRejectedOrder: ElementRef;
  @ViewChild('rejetedOrderList', { static: false }) set content(content: ElementRef) {
    if (content) {
      this.elemRejectedOrder = content;
      this.onAfterViewInit();
    }
  }

  rejectedOrderHeight = 0;
  lunchmateIcon = "assets/imgs/green_sandwich.png";
  lunchmateIconWhite = "assets/imgs/white_sandwich.png";
  lunchposIcon = "assets/imgs/LP_green.png";
  lunchposIconWhite = "assets/imgs/LP_white.png";
  // mainOrderData: OrderData[] = [];
  // processedOrderData: OrderData[] = [];
  // rejectedOrderData: OrderData[] = [];

  MOD: OD[] = [];
  POD: OD[] = [];
  ROD: OD[] = [];

  expandedMainOrderIndex = null;
  expandedProcessedOrderIndex = null;
  expandedRejectedOrderIndex = null;
  updateOrderTimer = null;
  updateOrderTimeout = 10 * 1000; //in ms
  animatedOrder = null;
  animationInProcess = false;
  // mainOrderItemState = null;

  constructor(public globalService: GlobalService, private navCtrl: NavController, private modalCtrl: ModalController, private loadingCtrl: LoadingController, private apisService: ApisService, private orderHandlers: OrderHandlersService, private alertsServices: AlertsServices, private localPrinterService: LocalPrinterService, private events: EventsService, private zone: NgZone) {
  }

  ngOnInit() {
  }

  ionViewDidEnter() {
    if (!this.globalService.allLoadingDone) {
      this.navCtrl.navigateRoot(['login']);
      return;
    }
    this.globalService.currentPage = "Orders";
    //
    setTimeout(() => {
      this.events.subscribe("api:cancelled", data => {
        console.log('api:cancelled', data);
        this.animationInProcess = false;
      });
      //
      this.events.subscribe("location:changed", data => {
        console.log('location:changed', data);
        // this.sendOrderLocationToServer(data);
      });
      //
      this.events.subscribe("order:prepared", data => {
        console.log('order:prepared', data);
        if (data.isQR || this.globalService.appSettings.currentDisplayType == 'dashboard') {
          this.animationInProcess = false;
          this.getOrders(false);
        }
        else {
          this.expandedMainOrderIndex = null;
          setTimeout(() => {
            this.animatedOrder.animationState = 'processed';
          }, 0);
        }
      });
      //
      this.events.subscribe("order:delivered", order_num => {
        console.log('order:delivered', order_num);
        this.getOrders(false);
      });
      //
      this.events.subscribe("clearprocessed:clicked", data => {
        console.log('clearprocessed:clicked');
        // this.clearProcessedOrders();
      });
      //
      this.events.subscribe("printlabel:called", order => {
        // this.printLabelClicked(order);
      });
      // console.log('events subscribed');
    }, 500);
    //
    this.getOrders(false);
  }

  async getOrders(refresher) {
    if (this.animationInProcess) {
      if (this.updateOrderTimer) {
        clearTimeout(this.updateOrderTimer);
      }
      this.updateOrderTimer = setTimeout(() => {
        this.getOrders(true);
      }, this.updateOrderTimeout);
      return;
    }
    //
    let post_data = {
      rest_id: this.globalService.assignedRestDetails.rest_id,
      is_all: true,
      user_id: this.globalService.assignedRestDetails.rest_admin_id,
      clear_processed_after_minutes: this.globalService.appSettings.clearProcessedAfterMinutes ? this.globalService.appSettings.clearProcessedAfterMinutes : null
    };
    let loader = await this.loadingCtrl.create({
      message: "Fetching orders..."
    });
    if (!refresher) {
      loader.present();
    }
    this.apisService.getOrders(post_data).subscribe((getOrdersSuccessResponse: any) => {
        console.log('getOrdersSuccessResponse', getOrdersSuccessResponse);
        loader.dismiss();
        if (getOrdersSuccessResponse.isSuccess) {

          if (this.animationInProcess) {
            if (this.updateOrderTimer) {
              clearTimeout(this.updateOrderTimer);
            }
            this.updateOrderTimer = setTimeout(() => {
              this.getOrders(true);
            }, this.updateOrderTimeout);
            return;
          }

          this.globalService.todaysTotalAmount = getOrdersSuccessResponse.todays_total;
          if (!refresher) {
            this.expandedMainOrderIndex = null;
            this.expandedProcessedOrderIndex = null;
            this.expandedRejectedOrderIndex = null;
          }
          this.mapOrdersData(getOrdersSuccessResponse.orders);
        }
        else {
          console.error(getOrdersSuccessResponse.message);
          if (refresher) {
            // this.alertsServices.presentAlert(null, getOrdersSuccessResponse.message);
          }
        }

        if (refresher) {
          // refresher.complete();
        }
        if (this.updateOrderTimer) {
          clearTimeout(this.updateOrderTimer);
        }
        this.updateOrderTimer = setTimeout(() => {
          this.getOrders(true);
        }, this.updateOrderTimeout);
      },
        getOrdersErrorResponse => {
          console.error({ getOrdersErrorResponse });
          loader.dismiss();
          if (refresher) {
            // this.alertsServices.presentAlert(null, this.apiServices.commonNetworkErrorMessage);
            // refresher.complete();
          }
          if (this.updateOrderTimer) {
            clearTimeout(this.updateOrderTimer);
          }
          this.updateOrderTimer = setTimeout(() => {
            this.getOrders(true);
          }, this.updateOrderTimeout);
        });
  }

  mapOrdersData(orders) {
    // console.log({ orders });
    this.orderHandlers.foodOrders = orders;
    // this.mainOrderData = [];
    // this.processedOrderData = [];
    // this.rejectedOrderData = [];
    this.MOD = [];
    this.POD = [];
    this.ROD = [];
    //
    for (var i = 0; i < orders.length; i++) {
      let thisOrder: OD = {
        order_id: orders[i].id,
        order_num: orders[i].order_num,
        order_acquire_datetime: this.getFormattedDateTime(orders[i].order_acquire_datetime),
        order_date: this.getFormattedDateTime(orders[i].order_printed_at),
        order_status: orders[i].order_status,
        can_be_rejected: orders[i].can_be_rejected,
        customer_name: orders[i].full_name,
        customer_phone_number: orders[i].phone_number,
        order_type_text: orders[i].order_type_text,
        order_type: orders[i].order_type,
        is_cleared_for_pos: orders[i].is_cleared_for_pos,
        animationState: null,
        items: [],
        order_time_remaining_status: orders[i].order_time_remaining_status
      };

      for (var j = 0; j < orders[i].order_description.items.length; j++) {
        let thisItem: ItemData = {
          item_name: orders[i].order_description.items[j].name,
          item_price: orders[i].order_description.items[j].itemPrice,
          total_allergens: orders[i].order_description.items[j].total_allergens,
          allergen_free_id: orders[i].order_description.items[j].allergen_free_id,
          additions_string: this.getFormattedAdditions(orders[i].order_description.items[j].additions),
          options_string: this.getFormattedOptions(orders[i].order_description.items[j].options)
        };
        //
        thisOrder.items.push(thisItem);
        let tempOrder: OrderData = {
          order_id: orders[i].id,
          order_num: orders[i].order_num,
          order_acquire_datetime: this.getFormattedDateTime(orders[i].order_acquire_datetime),
          order_date: this.getFormattedDateTime(orders[i].order_printed_at),
          order_status: orders[i].order_status,
          can_be_rejected: orders[i].can_be_rejected,
          item_name: orders[i].order_description.items[j].name,
          item_price: orders[i].order_description.items[j].itemPrice,
          total_allergens: orders[i].order_description.items[j].total_allergens,
          allergen_free_id: orders[i].order_description.items[j].allergen_free_id,
          additions_string: this.getFormattedAdditions(orders[i].order_description.items[j].additions),
          options_string: this.getFormattedOptions(orders[i].order_description.items[j].options),
          customer_name: orders[i].full_name,
          customer_phone_number: orders[i].phone_number,
          order_type_text: orders[i].order_type_text,
          order_type: orders[i].order_type,
          is_cleared_for_pos: orders[i].is_cleared_for_pos,
          animationState: null
        };
        //
        // if (this.globalService.appSettings.currentDisplayType == 'display') {
        //   if (orders[i].order_status == 3) {
        //     this.rejectedOrderData.push(tempOrder);
        //   }
        //   else if (orders[i].order_status == 5 || orders[i].order_status == 6) {
        //     this.processedOrderData.push(tempOrder);
        //   }
        //   else {
        //     this.mainOrderData.push(tempOrder);
        //   }
        // }
        // else {
        //   this.mainOrderData.push(tempOrder);
        // }
      }
      if (this.globalService.appSettings.currentDisplayType == 'display') {
        if (orders[i].order_status == 3) {
          this.ROD.push(thisOrder);
        }
        else if (orders[i].order_status == 5 || orders[i].order_status == 6) {
          this.POD.push(thisOrder);
        }
        else {
          this.MOD.push(thisOrder);
        }
      }
      else {
        this.MOD.push(thisOrder);
      }
    }
    // console.log(this.mainOrderData);
    // console.log(this.rejectedOrderData);
    // console.log(this.processedOrderData);
    // //
    // console.log(this.MOD);
    // console.log(this.ROD);
    // console.log(this.POD);
    this.orderHandlers.mainOrders = this.MOD;
    //
    // setTimeout(() => {
    //   if (this.elemRejectedOrder) {
    //     console.log(this.elemRejectedOrder.nativeElement.clientHeight);
    //     this.rejectedOrderHeight = this.elemRejectedOrder.nativeElement.offsetHeight;
    //     console.log('rejectedOrderHeight - ' + this.rejectedOrderHeight);
    //   }
    // }, 500);
  }

  getFormattedDateTime(date_time) {
    // let datetime = '' + new Date(date_time);
    // safari fix
    let datetime = '' + new Date(date_time.replace(/-/g, "/"));

    // let datetime_arr = datetime.split('GMTX ');
    // datetime = datetime_arr[0];
    // datetime = datetime.substring(0, (datetime.length - 4));

    // timezone fix
    datetime = datetime.substr(0, 21);
    return datetime;
  }

  getFormattedAdditions(additions) {
    let additionsString = '';
    for (let i = 0; i < additions.length; i++) {
      additionsString += additions[i].additionName + ', ';
    }
    additionsString = additionsString.replace(/,\s*$/, "");
    return additionsString;
  }

  getFormattedOptions(options) {
    let optionsString = '';
    for (var i = 0; i < options.length; i++) {
      optionsString += options[i].optionName + ', ';
    }
    optionsString = optionsString.replace(/,\s*$/, "");
    return optionsString;
  }

  popItemFromMainTriggerStart(ev) {
    // console.log('popItemFromMainTriggerStart');
    // console.log(ev.fromState + ' to ' + ev.toState);
    if (ev.toState == 'processed' || ev.toState == 'rejected') {
      // this.animationInProcess = true;
    }
  }

  popItemFromMainTriggerDone(ev) {
    // console.log('popItemFromMainTriggerDone');
    // console.log(ev.fromState + ' to ' + ev.toState);
    if (ev.toState == 'processed') {
      this.MOD.splice(this.MOD.indexOf(this.animatedOrder), 1);
      setTimeout(() => {
        this.animatedOrder.order_status = 5;
        this.POD.unshift(this.animatedOrder);
      }, 0);
    } else if (ev.toState == 'rejected') {
      this.MOD.splice(this.MOD.indexOf(this.animatedOrder), 1);
      setTimeout(() => {
        this.ROD.unshift(this.animatedOrder);
        setTimeout(() => {
          if (this.elemRejectedOrder) {
            this.rejectedOrderHeight = this.elemRejectedOrder.nativeElement.offsetHeight;
            // console.log(this.rejectedOrderHeight);
          }
        }, 50);
      }, 0);
    }
  }

  orderHeaderClicked(orderIndex, orderColumn) {
    if (this.isOrderExpanded(orderIndex, orderColumn)) {
      if (orderColumn == 'main') {
        this.expandedMainOrderIndex = null;
      } else if (orderColumn == 'processed') {
        this.expandedProcessedOrderIndex = null;
        // console.log(orderIndex, 'no expand');
      } else if (orderColumn == 'rejected') {
        this.expandedRejectedOrderIndex = null;
      }
    } else {
      if (orderColumn == 'main') {
        this.expandedMainOrderIndex = orderIndex;
      } else if (orderColumn == 'processed') {
        this.expandedProcessedOrderIndex = orderIndex;
        // console.log(orderIndex, 'expand');
      } else if (orderColumn == 'rejected') {
        this.expandedRejectedOrderIndex = orderIndex;
      }
    }
    if (orderColumn == 'rejected') {
      // console.log(this.rejectedOrderHeight);
      setTimeout(() => {
        if (this.elemRejectedOrder) {
          this.rejectedOrderHeight = this.elemRejectedOrder.nativeElement.offsetHeight;
          // console.log(this.rejectedOrderHeight);
        }
      }, 50);
    }
  }

  isOrderExpanded(orderIndex, orderColumn) {
    if (orderColumn == 'main') {
      return this.expandedMainOrderIndex === orderIndex;
    } else if (orderColumn == 'processed') {
      return this.expandedProcessedOrderIndex === orderIndex;
    } else if (orderColumn == 'rejected') {
      return this.expandedRejectedOrderIndex === orderIndex;
    }
  }

  printLabelClicked(order, order_item) {
    // event.stopPropagation();
    // console.log(order);
    order_item.order_num = order.order_num;
    order_item.customer_name = order.customer_name;
    if (this.globalService.appSettings.selectedPrinterIndex == 0) {
      this.localPrinterService.connectToEpsonLabelPrinter(order_item);
    }
    else if (this.globalService.appSettings.selectedPrinterIndex == 1) {
      this.localPrinterService.connectToHoneywellLabelPrinter(order_item);
    }
    else {
      this.alertsServices.presentAlert(null, 'Printer not found. Please select a printer in "Settings" dialog box.');
    }
  }



  callClicked(ev, order) {
    // event.stopPropagation();
    // console.log(order);
    if (!order.customer_phone_number) {
      this.alertsServices.presentAlert(null, 'Phone number is not available for this order.');
    }
    else {
      window.open('tel:' + order.customer_phone_number);
    }
  }

  rejectOrderClicked(order) {
    // event.stopPropagation();
    this.confirmReject(order);
  }

  async confirmReject(order) {
    // console.log(num);
    let rejectOrderModal = await this.modalCtrl.create(
      {
        component: RejectOrderPage,
        componentProps: {
          order_num: order,
          rest_name: this.globalService.assignedRestDetails.rest_name
        },
        showBackdrop: false,
        backdropDismiss: true,
        cssClass: "reject-order-modal"
      });
    rejectOrderModal.onDidDismiss().then(res => {
      console.log(res);
      let data = res.data;
      if (data) {
        this.rejectOrder(order, data);
      }
    });
    rejectOrderModal.present();
  }

  async rejectOrder(order, rejectMessage) {
    if (this.globalService.appSettings.currentDisplayType == 'display') {
      this.animationInProcess = true;
    }
    // console.log(order);
    // console.log(rejectMessage);
    //
    let loader = await this.loadingCtrl.create({
      message: "Rejecting order..."
    });
    let post_data = {
      id: order.order_id,
      rejected_message: rejectMessage,
      user_id: this.globalService.assignedRestDetails.rest_admin_id
    };
    loader.present();
    this.apisService.rejectOrder(post_data).subscribe(
      (response: any) => {
        console.log('rejectOrder ', response);
        loader.dismiss();
        if (response.isSuccess) {
          if (this.globalService.appSettings.currentDisplayType == 'dashboard') {
            this.alertsServices.presentAlert(
              "Order rejection:",
              "Order number #" + order.order_num + " has been rejected."
            );
          }
          // var spliceIndex = null;
          // this.toPrepareOrders.forEach((item, index) => {

          for (let i = this.MOD.length - 1; i >= 0; i--) {
            if (this.MOD[i].order_id == order.order_id) {
              this.MOD[i].order_status = 3;
              if (this.globalService.appSettings.currentDisplayType == 'display') {
                this.expandedMainOrderIndex = null;
                this.animatedOrder = this.MOD[i];
                setTimeout(() => {
                  this.animatedOrder.animationState = 'rejected';
                }, 0);
                // this.rejectedOrderData.unshift(this.mainOrderData[i]);
                // this.mainOrderData.splice(i, 1);
              }
              break;
            }
          }

          /*
          if (this.globalService.currentDisplayType == 'display') {
            this.expandedMainOrderIndex = null;
            this.expandedProcessedOrderIndex = null;
            this.expandedRejectedOrderIndex = null;

            setTimeout(() => {
              if (this.elemRejectedOrder) {
                this.rejectedOrderHeight = this.elemRejectedOrder.nativeElement.offsetHeight;
                // console.log(this.rejectedOrderHeight);
              }
            }, 50);
          }
          */
        } else {
          this.events.publish("api:cancelled", {});
          this.alertsServices.presentAlert(null, response.resultRes);
          // this.checkIfTokenExpired(response.resultRes);
        }
      },
      err => {
        this.events.publish("api:cancelled", {});
        console.error(err);
        loader.dismiss();
        alert(this.apisService.commonNetworkErrorMessage);
      }
    );
  }

  orderProcessedClicked(order) {
    // event.stopPropagation();
    if (this.globalService.appSettings.currentDisplayType == 'display') {
      this.animationInProcess = true;
    }
    this.animatedOrder = order;
    this.orderHandlers.markOrderAsPrepared(order.order_num, false, order);
  }

  pushItemInProcessedTriggerStart(ev) {
    // console.log('pushItemInProcessedTriggerStart');
    // console.log(ev.fromState + ' to ' + ev.toState);
  }

  pushItemInProcessedTriggerDone(ev) {
    // console.log('pushItemInProcessedTriggerDone');
    // console.log(ev.fromState + ' to ' + ev.toState);
    if (ev.toState == 'processed') {
      this.animationInProcess = false;
      this.animatedOrder = null;
      // this.getOrders(true);
    }

  }

  pushItemInRejectedTriggerStart(ev) {
    // console.log('pushItemInRejectedTriggerStart');
    // console.log(ev.fromState + ' to ' + ev.toState);
  }

  pushItemInRejectedTriggerDone(ev) {
    // console.log('pushItemInRejectedTriggerDone');
    // console.log(ev.fromState + ' to ' + ev.toState);
    if (ev.toState == 'rejected') {
      this.animationInProcess = false;
      this.animatedOrder = null;
      // this.getOrders(true);
    }
  }

  orderDeliveredClicked(order) {
    this.orderHandlers.markOrderAsDelivered(order.order_num);
  }

  trackOrderClicked(order) {
    console.log(order);
  // if (this.globalService.trackingStatus == 3) {
  //   if (this.globalService.trackedOrderNum == order.order_num) {
  //     this.locationhandler.stopTrack();
  //   }
  //   else {
  //     this.alertsServices.presentAlert(null, 'Tracking is active for order number "' + this.globalService.trackedOrderNum + '". Please "Stop Track" for that order before "Start Track" for this order.');
  //   }
  // }
  // else {
  //   this.globalService.trackingStatus = 2;
  //   this.locationhandler.fetchLocation(order.order_num).then((response) => {
  //     console.log('fetchLocation ', response);
  //   },
  //     (err) => {
  //       this.globalService.trackingStatus = 1;
  //       this.alertsServices.presentAlert(null, err);
  //       console.error(err);
  //     })
  //     .catch((excp) => {
  //       this.globalService.trackingStatus = 1;
  //       this.alertsServices.presentAlert(null, excp);
  //       console.error(excp);
  //     });
  // }

  }

  ionViewDidLeave() {
    this.events.unsubscribe("location:changed");
    this.events.unsubscribe("order:prepared");
    this.events.unsubscribe("order:delivered");
    this.events.unsubscribe("clearprocessed:clicked");
    this.events.unsubscribe("api:cancelled");
    if (this.updateOrderTimer) {
      clearTimeout(this.updateOrderTimer);
    }
  }

  // @HostListener("window:resize", ["$event"])
  // onResizeWindow() {
  //   // console.log('resized window');
  //   if (this.elemRejectedOrder) {
  //     this.rejectedOrderHeight = this.elemRejectedOrder.nativeElement.offsetHeight;
  //     // console.log('rejectedOrderHeight - ' + this.rejectedOrderHeight);
  //   }
  // }

  onAfterViewInit() {
    const observer = new ResizeObserver(entries => {
      // const height = entries[0].contentRect.height;
      // console.log(height);
      this.zone.run(() => {
        this.rejectedOrderHeight = this.elemRejectedOrder.nativeElement.offsetHeight;
      });
    });
    observer.observe(this.elemRejectedOrder.nativeElement);
  }

}
