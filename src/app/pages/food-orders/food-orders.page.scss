  .title-left {
    margin-top: 4px;
  }

  .title-right {
    margin-right: 16px;
    margin-top: -30px;
  }


  .title-ios {
    margin-top: 0px !important;
    padding: 0 10px 1px;
  }

  .title-right {
    color: white;
    margin-right: 10px;
    margin-top: -30px;
  }

  .toolbar-title-ios {
    // text-align: left;
  }

  .toolbar-content-ios {
    .title-right {
      margin-top: 0px;
    }
  }

  .lunchmate-icon {
    width: 28px;
  }

  .lm-icon-delivery {
    color: blue;
    font-size: 24px;
    margin-left:4px;
  }

  .lm-icon-order_tomorrow {
    color: red;
    font-size: 24px;
  }

  .left-item {
    // padding-top: 4px;
    max-width: 80%;
    
  }

  .title-left {
    margin-top: 4px;
  }

  .white-space-normal {
    white-space: normal;
  }

  .expanded-main-order-heading {
    background-color: #E1E1E1;
  }

  .processed-order-heading {
    background-color: var(--ion-color-lunchmate-green);


  }

  .custom-text div {
    color: #454545 !important
  }

  .expanded-processed-order-heading {
    background-color: #22891b;
  }

  .rejected-order-heading {
    background-color: var(--ion-color-reject);
  }

  .expanded-rejected-order-heading {
    background-color: #ca0808;
  }

  .processed-order-heading div,
  .rejected-order-heading div {
    color: white;
  }

  .processed-order-heading .lm-icon-order_tomorrow,
  .rejected-order-heading .lm-icon-order_tomorrow {
    color: white;
  }

  .processed-order-heading .lm-icon-delivery,
  .rejected-order-heading .lm-icon-delivery {
    color: white;
  }

  .rejected-order {
    color: red;
  }

  .accepted-order {
    color: green;
  }

  .prepared-order-list {
    overflow-y: auto;
    border: 1px solid lightgrey;
  }

  .prepared-order-list ion-toolbar {
    background: white;
    position: absolute;
    border: 1px solid lightgrey;
    width: calc(100% - 12px);
    margin-top: -1px;
  }

  .rejected-order-list {
    //background: red;
    max-height: 50%;
    overflow-y: auto;
    border: 1px solid lightgrey;
    padding: 1px
  }

  .rejected-order-list ion-row {
    padding-top: 50px;
  }

  .rejected-order-list ion-toolbar {
    background: white;
    position: absolute;
    border: 1px solid lightgrey;
    width: calc(100% - 12px);
    margin-top: -1px;
  }

  .order-item {
    border: 1px solid #ddd;
    padding: 5px;
    margin-bottom: 5px;
  }

  .order-type-text {
    margin-top: 8px;
  }

  .pulsating-circle {
    // position: absolute;
    // left: 50%;
    // top: 50%;
    transform: translateX(-50%) translateY(-50%);
    width: 30px;
    height: 30px;
    float: right;
    margin-top: 20px;
    margin-right: 10px;

    &:before {
      content: '';
      position: relative;
      display: block;
      width: 200%;
      height: 200%;
      box-sizing: border-box;
      margin-left: -50%;
      margin-top: -50%;
      border-radius: 45px;
      background-color: var(--ion-color-lunchmate-green);
      animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
    }

    &:after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      display: block;
      width: 100%;
      height: 100%;
      background-color: white;
      border-radius: 15px;
      box-shadow: 0 0 8px rgba(0, 0, 0, .3);
      animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -.4s infinite;
    }
  }

  .shaky {
    display: inline-block;
    // position: relative;
    animation-name: shake;
    animation-duration: 0.8s;
    transform-origin: 50% 50%;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }

  @keyframes shake {
    0% {
      transform: translate(2px, 1px) rotate(0deg);
    }

    10% {
      transform: translate(-1px, -2px) rotate(-1deg);
    }

    20% {
      transform: translate(-3px, 0px) rotate(1deg);
    }

    30% {
      transform: translate(0px, 2px) rotate(0deg);
    }

    40% {
      transform: translate(1px, -1px) rotate(1deg);
    }

    50% {
      transform: translate(-1px, 2px) rotate(-1deg);
    }

    60% {
      transform: translate(-3px, 1px) rotate(0deg);
    }

    70% {
      transform: translate(2px, 1px) rotate(-1deg);
    }

    80% {
      transform: translate(-1px, -1px) rotate(1deg);
    }

    90% {
      transform: translate(2px, 2px) rotate(0deg);
    }

    100% {
      transform: translate(1px, -2px) rotate(-1deg);
    }
  }

  @keyframes pulse-ring {
    0% {
      transform: scale(.33);
    }

    80%,
    100% {
      opacity: 0;
    }
  }

  @keyframes pulse-dot {
    0% {
      transform: scale(.2);
    }

    50% {
      transform: scale(1);
    }

    100% {
      transform: scale(.2);
    }
  }

  ion-card-header{
    padding-top: 24px;
    padding-bottom: 38px;
  }

  ion-card-content{
    padding-top:16px;
  }

  .padding-top-0{
    padding-top:0px !important;
  }