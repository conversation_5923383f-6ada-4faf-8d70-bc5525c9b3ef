<ion-header>
  <ion-toolbar color="primary">
    <ion-title>
      Lunchmate Dashboard - Settings
    </ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>
  <div class="side-menu">
    <app-side-menu></app-side-menu>
  </div>
  <div class="main-content">
    <ion-list style="margin-bottom:0px;">
      <div class="section-title">
        <strong>
          Label Printer
        </strong>
        <ion-item>
          <ion-label position="stacked">Select the label printer type:</ion-label>
          <ion-select class="full-width" placeholder="Select printer" [(ngModel)]="appSettings.selectedPrinterIndex"
            (ionChange)="checkPrinterStatus()">
            <ion-select-option value="0">EPSON TMi printer</ion-select-option>
            <ion-select-option value="1">Honeywell label printer</ion-select-option>
          </ion-select>
        </ion-item>
      </div>
    </ion-list>
    <ion-item *ngIf="appSettings.selectedPrinterIndex == 0 && availablePrinters.length">
      <ion-label position="floating"> Enter printer label</ion-label>
      <ion-input type="text" [(ngModel)]="availablePrinters[appSettings.selectedPrinterIndex].printerLabel">
      </ion-input>
    </ion-item>
    <ion-item style="margin-bottom:8px;" *ngIf="appSettings.selectedPrinterIndex == 0 && availablePrinters.length">
      <ion-label position="floating"> <span style="float:left;">Enter printer Ip address </span>
        <ion-icon class="printer-status-check" name="checkmark-circle" *ngIf="printerStatus == 3"></ion-icon>
      </ion-label>
      <ion-input type="text" [(ngModel)]="availablePrinters[appSettings.selectedPrinterIndex].printerIpAddress"
        (keyup)="checkPrinterStatus()">
      </ion-input>
    </ion-item>
    <ion-row>
      <ion-col>
        <div class="section-title">
          <strong>
            Screen settings
          </strong>
          <div>
            <ion-item lines="none">
              <ion-label>Blank screen when inactive</ion-label>
              <ion-checkbox slot="start" color="primary" [(ngModel)]="appSettings.displayIdleScreen">
              </ion-checkbox>
            </ion-item>
            <ion-item>
              <ion-label position="stacked">Enter number of minutes of inactivity</ion-label>
              <ion-input name="username" type="text" value="" [(ngModel)]="appSettings.numberOfMinuteOfInactivity"
                required>
              </ion-input>
            </ion-item>
          </div>
        </div>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <div class="section-title">
          <strong>
            General settings
          </strong>
          <div class="ion-padding">
            <ion-item class="clr-proc-inpt">
              <ion-label position="stacked">Enter number of minutes after which processed orders will be cleared</ion-label>
              <ion-input type="text" [(ngModel)]="appSettings.clearProcessedAfterMinutes"
                required>
              </ion-input>
            </ion-item>
            <ion-button
              (click)="clearProcessedClick()" size="small" color="primary" expand="block" fill="outline">
              Clear Processed Orders Now
            </ion-button>
            <!-- <br> -->
            <ion-button (click)="pinSettingsClick()"
              size="small" color="primary" expand="block" fill="outline" class="ion-margin-top">
              Set Your Pin
            </ion-button>
            </div>
            </div>
      </ion-col>
      </ion-row>

      <ion-button class="btn-save" (click)="okClick()" fill="solid">Save</ion-button>

      </div>
</ion-content>
<!-- <ion-footer>
  <ion-toolbar class="ion-text-center">
    <ion-button (click)="cancelClick()" color="danger" fill="clear">
      Cancel
    </ion-button>
    <ion-button (click)="okClick()" fill="solid">
      Ok
    </ion-button>
  </ion-toolbar>
</ion-footer> -->