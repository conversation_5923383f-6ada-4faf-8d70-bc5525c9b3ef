import { Component, OnInit } from '@angular/core';
import { LoadingController, ModalController, NavController, } from '@ionic/angular';
import { GlobalService } from '../../_providers/global.service';
import { LocalPrinterService } from '../../_providers/local-printer.service';
import { SetPinPage } from 'src/app/modals/set-pin/set-pin.page';
import { ApisService } from 'src/app/_providers/api.service';
import { AlertsServices } from 'src/app/_providers/alert.service';
import { Storage } from '@ionic/storage';



@Component({
  selector: 'app-settings',
  templateUrl: './settings.page.html',
  styleUrls: ['./settings.page.scss'],
})
export class SettingsPage implements OnInit {
  gaming;
  availablePrinters = [];
  appSettings: any = { selectedPrinterIndex: 0 };
  printerStatus = 1;
  checkPrinterStatusTimeout;
  printerStatusTop = 176;
  isModalOpen = false;
  pin;
  c_pin;
  constructor(public navCtrl: NavController, public globalService: GlobalService, private localPrinterService: LocalPrinterService, private modalCtrl: ModalController, private loadingCtrl: LoadingController, private apisService: ApisService, private alertsServices: AlertsServices, private storage: Storage) {
  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    if (!this.globalService.allLoadingDone) {
      this.navCtrl.navigateRoot(['login']);
      return;
    }
    this.globalService.currentPage = 'Settings';
    //
    this.availablePrinters = JSON.parse(JSON.stringify(this.globalService.availablePrinters));
    this.appSettings = JSON.parse(JSON.stringify(this.globalService.appSettings));
    if (!this.appSettings.selectedPrinterIndex) this.appSettings.selectedPrinterIndex = '0';
    console.log(this.availablePrinters);
    console.log(this.appSettings);

    //
    this.checkPrinterStatus();
  }

  checkPrinterStatus() {
    if (this.checkPrinterStatusTimeout) {
      clearTimeout(this.checkPrinterStatusTimeout);
    }
    this.printerStatus = 1;
    this.checkPrinterStatusTimeout = setTimeout(() => {
      console.log(this.appSettings.selectedPrinterIndex);
      let printerIpAddress = this.availablePrinters[this.appSettings.selectedPrinterIndex].printerIpAddress;
      let splitIp = printerIpAddress.split('.');
      if (splitIp.length == 4) {
        if (splitIp[3] > 999) {
          this.printerStatus = 2;
          return;
        }
      }
      else {
        this.printerStatus = 2;
        return;
      }

      this.localPrinterService.checkPrinterStatus(this.appSettings.selectedPrinterIndex, printerIpAddress).subscribe(
        response => {
          console.log('checkPrinterStatus ', response);
          this.printerStatus = 3;
        },
        err => {
          console.error(err);
          this.printerStatus = 2;
        }
      );
    }, 1000);
  }

  async pinSettingsClick() {
    let modal = await this.modalCtrl.create({
      component: SetPinPage,
      componentProps: {},
      showBackdrop: false,
      backdropDismiss: true,
      cssClass: "set-pin-modal"
    });
    modal.present();
    modal.onDidDismiss().then(async res => {
      // console.log({ res });
      let data = res.data;
      if (data && data != 'CANCEL') {
        let loader = await this.loadingCtrl.create({
          message: "Setting PIN..."
        });
        loader.present();
        let post_data = {
          user_id: this.globalService.assignedRestDetails.rest_admin_id,
          lunchpos_pin: data
        };
        console.log({ post_data });
        this.apisService.setPinFromDashboard(post_data).subscribe((response: any) => {
          console.log(response);
          if (response.isSuccess) {
            this.alertsServices.presentAlert(null, 'Your PIN has been set successfully.');
          } else {
            this.alertsServices.presentAlert(null, response.message);
          }
          loader.dismiss();
        },
          err => {
            loader.dismiss();
            console.error(err);
            this.alertsServices.presentAlert(null, this.apisService.commonNetworkErrorMessage);
          });
      }
    });
  }

  okClick() {
    this.globalService.availablePrinters = this.availablePrinters;
    this.globalService.appSettings = this.appSettings;
    this.storage.set("availablePrintersDashboard", this.globalService.availablePrinters);
    this.storage.set("appSettingsDashboard", this.globalService.appSettings);
    this.storage.set("appSettingsPos", this.globalService.appSettings);
    this.globalService.setSleepMode();
    this.alertsServices.presentAlert(null, 'Your settings have been saved successfully.').then(() => {
      setTimeout(() => {
        this.navCtrl.back();
      }, 0);
    });
  }

  async clearProcessedClick() {
    let loader = await this.loadingCtrl.create({
      message: "Clearing processed orders..."
    });
    let post_data = {
      rest_id: this.globalService.assignedRestDetails.rest_id,
      user_id: this.globalService.assignedRestDetails.rest_admin_id
    };
    loader.present();
    this.apisService.clearProcessedOrders(post_data).subscribe(
      (clearProcessedOrdersSuccessResponse: any) => {
        console.log('clearProcessedOrders', clearProcessedOrdersSuccessResponse);
        if (clearProcessedOrdersSuccessResponse.isSuccess) {
          // this.ROD = [];
          // this.POD = [];
          // this.getPreparedOrders();
          // this.getRejectedOrders();
          // this.expandedProcessedOrderIndex = null;
          // this.expandedRejectedOrderIndex = null;
          // setTimeout(() => {
          //     this.rejectedOrderHeight = 0;//this.elemRejectedOrder.nativeElement.offsetHeight;
          //     console.log(this.rejectedOrderHeight);
          // }, 50);
        } else {
          alert(clearProcessedOrdersSuccessResponse.message);
        }
        loader.dismiss();
      },
      clearProcessedOrdersErrorResponse => {
        console.error(clearProcessedOrdersErrorResponse);
        loader.dismiss();
        alert(this.apisService.commonNetworkErrorMessage);
      }
    );
  }
}
