import { ToastService } from '../../_providers/toast.service';
import { GlobalService } from '../../_providers/global.service';
import { ApisService } from '../../_providers/api.service';
import { LoadingService } from '../../_providers/loading.service';
import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { LoadingController, ModalController, NavController } from '@ionic/angular';
import { AlertsServices } from 'src/app/_providers/alert.service';
import { VoucherPage } from 'src/app/modals/voucher/voucher.page';
import { LocalPrinterService } from 'src/app/_providers/local-printer.service';

@Component({
  selector: 'app-vouchers',
  templateUrl: './vouchers.page.html',
  styleUrls: ['./vouchers.page.scss'],
})
export class VouchersPage implements OnInit {
  vouchers: any = [];
  voucher: any;
  isModalOpen = false;
  @ViewChild("voucherImage", {
    read: ElementRef
  })
  voucherImage;

  constructor(
    private loadingCtrl: LoadingController,
    private apisService: ApisService,
    public globalService: GlobalService,
    private toastService: ToastService,
    private navCtrl: NavController,
    private alertsServices: AlertsServices,
    private modalCtrl: ModalController,
    private localPrinterService: LocalPrinterService
  ) {

  }

  ngOnInit() {
  }

  ionViewDidEnter() {
    if (!this.globalService.allLoadingDone) {
      this.navCtrl.navigateRoot(['login']);
      return;
    }
    this.globalService.currentPage = 'Vouchers';
    this.loadVouchers();
  }

  async loadVouchers() {
    let post_data = {
      rest_id: this.globalService.assignedRestDetails.rest_id,
      user_id: this.globalService.assignedRestDetails.rest_admin_id
    };
    let loader = await this.loadingCtrl.create({
      message: "Fetching vouchers..."
    });
    loader.present();
    this.apisService.getVouchersListForDashboard(post_data).subscribe((res: any) => {
        console.log(res);
        if (res.isSuccess) {
          this.vouchers = res.tokens;
        } else {
          // this.toastService.toastServices(res.message, 'danger', 'top');
          this.alertsServices.presentAlert(null, res.message);
        }
      loader.dismiss();
      }, err => {
      // this.toastService.toastServices(this.apiService.commonNetworkErrorMessage, 'danger', 'top');
      console.error(err);
      loader.dismiss();
      this.alertsServices.presentAlert(null, this.apisService.commonNetworkErrorMessage);
    })
  }

  printClick() {
    let voucherImageElement = this.voucherImage.nativeElement;
    var canvas = document.createElement("canvas");
    let originalWidth = voucherImageElement.width;
    let originalHeight = voucherImageElement.height;
    // we will change to width 300px, height will set relatively
    let newWidth = 300;
    let newHeight = Math.floor(
      (newWidth / originalWidth) * originalHeight
    );
    //
    canvas.width = newWidth;
    canvas.height = newHeight;
    var ctx = canvas.getContext("2d");
    ctx.drawImage(voucherImageElement, 0, 0, newWidth, newHeight);
  }

  async voucherClick(voucher) {
    // console.log(voucher);
    let voucherModal = await this.modalCtrl.create(
      {
        component: VoucherPage,
        componentProps: {
          voucher: voucher
        },
        showBackdrop: false,
        backdropDismiss: true,
        cssClass: "voucher-modal"
      });
    voucherModal.onDidDismiss().then(res => {
      // console.log(res);
      let data = res.data;
      if (data) {
        if (data.canvasContext) {
          this.printVoucher(voucher, data.canvasContext);
        }
      }
    });
    voucherModal.present();
  }

  async printVoucher(voucher, canvasContext) {
    // console.log(canvasContext);
    let loader = await this.loadingCtrl.create({
      message: "Printing voucher..."
    });
    loader.present();
    this.localPrinterService.getEpsonLibLoaded().then(resolve => {
      // console.log(resolve);
      this.localPrinterService.getVoucherDataForPrint(voucher, canvasContext).then(data => {
        let voucherXml = data.toString();
        voucherXml =
          '<?xml version="1.0" encoding="utf-8"?><PrintRequestInfo><ePOSPrint><Parameter><devid>local_printer</devid><timeout>10000</timeout></Parameter><PrintData>' +
          voucherXml +
          "</PrintData></ePOSPrint></PrintRequestInfo>";
        let post_data = {
          rest_id: this.globalService.assignedRestDetails.rest_id,
          user_id: this.globalService.assignedRestDetails.rest_admin_id,
          token_code: voucher.token_code,
          token_xml: voucherXml
        };
        //   console.log('post_data', post_data);
        this.apisService.printTokenFromDashboard(post_data).subscribe(
          (successResponse: any) => {
            console.log('successResponse printTokenFromDashboard ', successResponse);
            loader.dismiss();
            if (successResponse.isSuccess) {
              this.alertsServices.presentAlert(null, 'A request to print this token has been sent to the restaurant printer.')
            } else {
              this.alertsServices.presentAlert(null, successResponse.message);
            }
          },
          err => {
            console.error(err);
            loader.dismiss();
            // alert common network error message
            this.alertsServices.presentAlert(null, this.apisService.commonNetworkErrorMessage);
          });
      });
    }, reject => {
      loader.dismiss();
      console.error(reject);
    });
  }

}
