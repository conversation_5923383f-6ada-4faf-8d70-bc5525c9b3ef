<ion-header>
  <ion-toolbar color="primary">
    <ion-title>
      Lunchmate Dashboard - Vouchers
    </ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>
  <div class="side-menu">
    <app-side-menu></app-side-menu>
  </div>
  <div class="main-content">
  <!-- <ion-row>
    <ion-col> -->
  <ion-card class="voucher-card" *ngFor="let voucher of vouchers" (click)="voucherClick(voucher)">
        <ion-row>
          <ion-col size="2" class="ion-align-self-center ion-text-center" >
            <ion-avatar>
              <img src="assets/imgs/tokenCode.png">
            </ion-avatar>
          </ion-col>
          <ion-col size="8" class="ion-text-start">
            <ion-label>
              <h2><b>{{voucher.token_code}}</b></h2>
              <h3><b>{{voucher.token_name}}</b></h3>
              <p>{{voucher.destroy_date}}</p>
            </ion-label>
          </ion-col>
          <ion-col size="2" class="ion-align-self-center ion-text-end">
            <ion-label><h2><b>{{voucher.amount | currency:'GBP'}}</b></h2></ion-label>
          </ion-col>
        </ion-row>
      </ion-card>
      <ion-card class="voucher-card" *ngIf="!vouchers.length">
        <ion-row>
          <ion-col>
            No voucher has been generated for this restaurant yet.
          </ion-col>
        </ion-row>
      </ion-card>
    <!-- </ion-col>
  </ion-row> -->
</div>
</ion-content>