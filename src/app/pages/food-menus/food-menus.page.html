<ion-header>
  <ion-toolbar color="primary">
    <ion-title>Lunchmate Dashboard - Food menus</ion-title>
    <ion-buttons slot="end" class="ion-padding menu-buttons">
      <ion-button (click)="saveClicked(selectedCourse)" class="save-btn" color="primary" fill="solid"
        [disabled]="!isModified">
        Save
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="side-menu">
    <app-side-menu></app-side-menu>
  </div>
  <div class="main-content">
    <ion-row style="height:100%">
  
      <ion-col style="height:100%">
        <div class="main">
          <ion-card style="width: 100%;margin:0px;">
            <div class="column-1">
              <ul class="vertical-menu">
                <li *ngFor="let course of menuCategories; let i=index" (click)="courseTabClicked(course,i)"
                  [ngClass]="{'active':selectedTabIndex === i}">
  
                  {{course.courseName}}
                </li>
  
              </ul>
            </div>
            <div class="column-2">
              <ion-list>
                <!-- {{selectedCourse |json}} -->
                  <div class="menu-item-container ion-padding"
                    *ngFor="let menuItem of selectedCourse.menuItems ; let j = index">
                      <!-- {{ menuItem | json }} -->
                  <ion-row class="row pgRow">
                    <ion-col class="col-md-6" size="6" style="padding-right: 0;">
                      <label class="label-text"> Title:</label>
                      <input type="text" class="custom-width" [(ngModel)]="menuItem.itemName" placeholder="title"
                        (keyup)="checkIsCategoryModified()">
                    </ion-col>
                    <!-- {{ menuItem | json }} -->
                    <ion-col class="col-md-3 ion-padding-left ion-text-end" size="3">
                      <label class="label-text">Price: </label>
                      <input type="text" style="width: 65%;" [(ngModel)]="menuItem.itemPrice" placeholder=" price"
                      *ngIf="!(menuItem.is_special_offer )" (keyup)="checkIsCategoryModified()">
                        <!-- *ngIf="!menuItem.is_special_offer" -->
                      <input type="text" style="width: 65%;" [(ngModel)]="menuItem.special_price" placeholder=" price"
                        *ngIf="menuItem.is_special_offer" (keyup)="checkIsCategoryModified()">
                        <!-- {{ menuItem.is_special_offer }}
                        {{ menuItem.itemPrice }}                         -->
                      <ion-icon [name]="menuItem.is_special_offer?'star':'star-outline'"
                        [ngStyle]="{color: menuItem.is_special_offer?'red':'black'}"
                        (click)="specialOfferStarClicked(selectedTabIndex,j)" class="custom-start-icon"
                        *ngIf="globalService.assignedRestDetails?.use_specials"></ion-icon>
  
                    </ion-col>
  
                    <ion-col class="col-md-3 ion-padding-left ion-text-end" style="padding-right: 5px;" size="3">
                      <ion-item no-lines style="text-align: end;">
                        <label class="label-text"> Published</label>
                        <!-- <ion-checkbox disableRipple [checked]="menuItem.isPublished" [(ngModel)]="menuItem.isPublished"
                        class="published">
  
                      </ion-checkbox> -->
                      </ion-item>

                      </ion-col>
                  </ion-row>
                  <ion-row class="row pgRow" row>
                    <ion-col style="padding-right: 0;" size="7">
                      <span class="label-text"
                        *ngIf="(menuItem.is_special_offer ) && globalService.assignedRestDetails.use_special_count">
                        This item will return to normal price after {{globalService.assignedRestDetails?.special_duration}}
                        day(s), or
                        as soon as a specific number of this item has
                        been sold.
                      </span>
                      </ion-col>
                      <ion-col size="2">
  
  
  
                      <div style="padding-right: 0;"
                        *ngIf="(menuItem.is_special_offer ) && globalService.assignedRestDetails?.use_special_count">
  
                        <input type="text" style="width: 100%;" class="ion-float-right"
                          [(ngModel)]="menuItem.special_count"
                          placeholder="Set item count" *ngIf="menuItem.is_special_offer "
                          (keyup)="checkIsCategoryModified()">
                      </div>
                    </ion-col>
                    <ion-col size="3" class="ion-text-center">
  
                      <ion-item no-lines style="text-align: center; margin-top: -20px;">
  
                        <ion-toggle color="success" (ngModelChange)="checkIsCategoryModified()"
                          [checked]="menuItem.isPublished" [(ngModel)]="menuItem.isPublished" class="custom-toggle">
                        </ion-toggle>
                      </ion-item>
                    </ion-col>
  
                  </ion-row>
  
                </div>
              </ion-list>
            </div>
          </ion-card>
        </div>
      </ion-col>
  
      <ion-col style="height:100%" *ngIf="!menuCategories.length">
        <div class="main ion-padding">
          <ion-card>
            <ion-row>
              <ion-col class="ion-padding">
                No menu-items to display here!
              </ion-col>
            </ion-row>
          </ion-card>
        </div>
      </ion-col>
    </ion-row>
  </div>
</ion-content>
