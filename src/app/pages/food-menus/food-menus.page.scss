    .title-ios {
      margin-top: 0px !important;
      padding: 0 10px 1px;
    }
  
    .toolbar-title-ios {
      // text-align: left;
    }
  
    .left-item {
      padding-top: 4px;
      max-width: 80%;
    }
  
    .expanded-category {
      background-color: #E1E1E1;
    }
  
    .clear-float {
      clear: both;
    }
  
    .pgRow {
        width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  
    .form-control {
      display: block;
      width: 100%;
    }
  
    .custom-start-icon {
      position: absolute;
      top: 8px;
      right: 15px;
  
    }
  
  
    /////
    .save-btn {
      border: 1px solid white;
    }
  
    .label-text {
      display: inline-block;
      color: gray;
    }
  
    input {
      // color: gray;
      // border-color: grey;
      // ;
      border: 1px solid #c8c8c8;
      padding: 3px;
      background: white;;
    }
  
    .menu-item-container {
      background: white;
      border-bottom: 1px solid #c8c8c8;
      padding-bottom: 0;
      margin-left: 16px;
      padding-left: 0;
    }
  
    .main {
      width: 100%;
      display: flex;
      background-color: #f2f2f2;
      height: 100%;
  
      .column-1 {
        width: 25%;
        float: left;
        background-color: #c7e3c8;
        max-height: 100%;
        overflow: auto;
      }
  
      .column-2 {
        width: 75%;
        // float: left;
        background-color: #fff;
        overflow: auto;
        max-height: 100%;
  
        // div {
        //   padding: 20px;
        // }
      }
    }
  
    .active {
      outline: 1px solid white;
      cursor: pointer;
      background-color: white;
      color: var(--ion-color-lunchmate-green);
    }
  
    .vertical-menu {
  
      display: block !important;
      width: 100%;
      text-align: center;
      list-style-type: none;
      padding: 0px;
      margin-top: 0px;
      margin-bottom: 0;
    }
  
    li {
      // color: #3da735;
      color: var(--ion-color-lunchmate-green);
      font-style: normal;
      // font-weight: normal;
      font-weight: 400;
      text-align: left;
      padding: 16px;
      cursor: pointer;
      font-size: 20px;
      border-bottom: 1px solid;
    }
  
    li:after {
      display: none;
    }
  
    a {
      padding: 8px;
    }
  
    ion-toggle {
      // margin-right: 25px !important;
      width: 50px !important;
      height: 25px !important;
  
      .toggle-inner {
        height: 30px !important;
        width: 30px !important;
        left: 8px !important;
        top: 0px !important;
      }
    }
  
    .menu-buttons{
      height:56px;
    }
    /////
//   }
  