import { Component, OnInit } from '@angular/core';
import { Alert<PERSON>ontroller, LoadingController, NavController } from '@ionic/angular';
import { AlertsServices } from 'src/app/_providers/alert.service';
import { ApisService } from 'src/app/_providers/api.service';
import { GlobalService } from 'src/app/_providers/global.service';

@Component({
  selector: 'app-food-menus',
  templateUrl: './food-menus.page.html',
  styleUrls: ['./food-menus.page.scss'],
})
export class FoodMenusPage implements OnInit {

  menuCategories: any[] = [];
  expandedCategoryIndex = null;
  dailyItemsAllowed = false;
  dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  specialOffersAddedTodayItemsArr: any = [];
  specialOffersRemovedTodayItemsArr: any = [];

  selectedTabIndex = 0;
  selectedCourse: any = {};
  isModified = false;
  menuItemsOriginal: any;

  constructor(
    private apiServices: ApisService, public globalService: GlobalService,
    private alertsServices: AlertsServices, public loadingCtrl: LoadingController, public alertController: AlertController, private navCtrl: NavController
  ) {
  }

  ngOnInit() {

  }

  ionViewDidEnter() {
    // console.log('ionViewDidLoad FoodMenusPage');
    if (!this.globalService.allLoadingDone) {
      this.navCtrl.navigateRoot(['login']);
      return;
    }
    this.globalService.currentPage = 'Menus';
    this.getMenus();
  }

  async getMenus() {
    console.log(this.globalService);
    this.menuCategories = [];
    this.expandedCategoryIndex = null;
    const post_data = {
      restID: this.globalService.assignedRestDetails.rest_id,
      user_id: this.globalService.assignedRestDetails.rest_admin_id
    };
    const loader = await this.loadingCtrl.create({
      message: 'Fetching menuitems...'
    });
    await loader.present();
    this.apiServices.getMenuItems(post_data).subscribe((getMenuItemsSuccessResponse: any) => {
      console.log('getMenuItemsSuccessResponse ', getMenuItemsSuccessResponse);
      if (getMenuItemsSuccessResponse.isSuccess === true) {
        this.dailyItemsAllowed = getMenuItemsSuccessResponse.allow_daily_items;
        this.menuCategories = getMenuItemsSuccessResponse.courses;
        this.selectedCourse = this.menuCategories[0];
        this.menuItemsOriginal = JSON.parse(JSON.stringify(this.menuCategories));
      }
      else {
        console.error(getMenuItemsSuccessResponse.message);
        this.alertsServices.presentAlert(null, getMenuItemsSuccessResponse.message);
      }
      loader.dismiss();
    },
      getMenuItemsErrorResponse => {
        console.error({ getMenuItemsErrorResponse });
        loader.dismiss();
        this.alertsServices.presentAlert(null, this.apiServices.commonNetworkErrorMessage);
      });
  }

  categoryHeaderClicked(categoryIndex) {
    //console.log(courseNum);
    if (this.isCategoryExpanded(categoryIndex)) {
      this.expandedCategoryIndex = null;
    } else {
      this.expandedCategoryIndex = categoryIndex;
    }
    //console.log(this.courses);
  }

  isCategoryExpanded(categoryIndex) {
    return this.expandedCategoryIndex === categoryIndex;
  }

  async saveClicked(category) {
    console.log({ category });
    // setAllMenuItems

    // console.log(menuItem);

    let post_data = {
      all_menu_items: category.menuItems,
      rest_id: this.globalService.assignedRestDetails.rest_id,
      offer_added_today: this.specialOffersAddedTodayItemsArr,
      offer_removed_today: this.specialOffersRemovedTodayItemsArr
    };

    const loader = await this.loadingCtrl.create({
      message: 'Updating menuitem...'
    });
    loader.present();

    this.apiServices.setAllMenuItems(post_data).subscribe((response: any) => {
      console.log('setAllMenuItems ', response);
      if (response.isSuccess) {
        this.isModified = false;
      }
      else {
        this.alertsServices.presentAlert(null, response.message);
      }
      loader.dismiss();
      // if (refresher) {
      // 	refresher.complete();
      // }
    },
      err => {
        console.error(err);
        loader.dismiss();
        this.alertsServices.presentAlert(null, this.apiServices.commonNetworkErrorMessage);
      });

  }

  specialOfferStarClicked(i, j) {
    this.checkIsCategoryModified();
    console.log('specialOfferStarClicked', this.menuCategories[i].menuItems[j]);
    // if (1) {
    //   return;
    // }
    const menuItem = this.menuCategories[i].menuItems[j];
    if (Number(menuItem.special_price)) {
      if (!menuItem.is_special_offer) {
        if (!this.specialOffersAddedTodayItemsArr.includes(menuItem.itemID)) {
          this.specialOffersAddedTodayItemsArr.push(menuItem.itemID);
          // if we need to save date time from javascript:
          // this.menuItems[i].special_date = this.getCurrentDateTime();
        }
        const idxForRemove = this.specialOffersRemovedTodayItemsArr.indexOf(menuItem.itemID);
        // console.log({ idxForRemove });
        if (idxForRemove !== -1) {
          this.specialOffersRemovedTodayItemsArr.splice(idxForRemove, 1);

        }
      }
      else {

        const idxForAdd = this.specialOffersAddedTodayItemsArr.indexOf(menuItem.itemID);
        console.log({ idxForAdd });
        if (idxForAdd !== -1) {
          this.specialOffersAddedTodayItemsArr.splice(idxForAdd, 1);
        }
        if (!this.specialOffersRemovedTodayItemsArr.includes(menuItem.itemID)) {
          this.specialOffersRemovedTodayItemsArr.push(menuItem.itemID);

        }

      }
      menuItem.is_special_offer = !menuItem.is_special_offer;

    } else {

      this.presentSpecialOfferPrompt(i, j);

    }

  }

  async presentSpecialOfferPrompt(i, j) {
    let price = this.menuCategories[i].menuItems[j].itemPrice;
    let alert = await this.alertController.create({
      message: 'Special offer price',
      inputs: [
        {
          name: 'price',
          value: price,
          placeholder: 'Special offer price'
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          handler: data => {
            console.log('Cancel clicked');

          }
        },
        {
          text: 'Ok',
          handler: data => {
            if (data) {

              console.log(data.price);
              this.menuCategories[i].menuItems[j].special_price = data.price;
              this.menuCategories[i].menuItems[j].is_special_offer = 1;
              this.specialOffersAddedTodayItemsArr.push(this.menuCategories[i].menuItems[j].itemID);
              // logged in!
            } else {
              // invalid login
              return false;
            }
          }
        }
      ]
    });
    alert.present();
  }

  courseTabClicked(course, index) {
    console.log({ course });
    if (this.isModified) {

      this.alertsServices.presentConfirm('Modified',
        'You have made modifications on this page. What would you like to do?', 'Save', 'Discard').then((save) => {
          console.log('save');
          this.saveClicked(this.selectedCourse);
          this.setNextTab(course, index);
        },
          discard => {
            console.log('discard');
            this.setOriginalMenuItems(course, index);
          }

        );
    } else {
      this.setNextTab(course, index);
    }

  }

  setOriginalMenuItems(course, index) {
    // if (1) {
    //   console.log(course, index);
    //   //  return;

    // }
    let selectedCourse = this.selectedCourse;
    // let menuItemsOriginalForCategory = this.menuItemsOriginal.filter(item => item.courseID == selectedCourse.courseID);
    let indexToupdate = this.menuCategories.findIndex((item) => item.courseID === selectedCourse.courseID);
    console.log({ indexToupdate });
    // menuItemsOriginalForCategory.forEach(newItem => {
    //   let updateItem = this.menuCategories.find(this.findIndexToUpdate, newItem.itemID);
    //   let index = this.menuCategories.indexOf(updateItem);
    //   this.menuCategories[index] = JSON.parse(JSON.stringify(newItem));
    // });
    const updateItem = this.menuItemsOriginal[indexToupdate];
    this.menuCategories[indexToupdate] = JSON.parse(JSON.stringify(updateItem));
    this.setNextTab(course, index);
  }

  setNextTab(course, index) {
    this.selectedCourse = course;
    this.selectedTabIndex = index;
    this.isModified = false;
  }

  checkIsCategoryModified() {
    this.isModified = true;
  }

}
