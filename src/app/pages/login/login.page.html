<ion-content class="ion-padding">
  <!-- <div class="login-board"> -->
    <!-- <form #login_form="ngForm" novalidate>
      <ion-row>
        <ion-col>
          <ion-button (click)="quitClick()" color="light" class="ion-float-left"  fill="solid">
            Quit
          </ion-button>
        </ion-col>
        <ion-col>
          <ion-button type="submit" (click)="loginClick()" color="light" class="ion-float-right"  fill="solid" [disabled]="!login_form.valid">
            Sign In
          </ion-button>
        </ion-col>
      </ion-row>

      <ion-row margin-top margin-bottom>
        <ion-col class="ion-text-center">
          <img class="logo" src="assets/imgs/lunchmate_white_logo.png">
        </ion-col>
      </ion-row>
      <ion-row margin-top margin-bottom>
        <ion-col>
          <ion-list>
            <ion-item>
              <ion-input name="username" type="text" placeholder="Username" [(ngModel)]="username" required></ion-input>
            </ion-item>

            <ion-item>
              <ion-input name="password" type="password" placeholder="Password" [(ngModel)]="password" required>
              </ion-input>
            </ion-item>

            <ion-item lines="none">
              <ion-label>Stay signed in</ion-label>
              <ion-checkbox slot="start" color="primary" name="staySignedIn" [(ngModel)]="staySignedIn"></ion-checkbox>
            </ion-item>
          </ion-list>

        </ion-col>
      </ion-row>
    </form> -->





    <!-- <div class="login-panel" color="lunchmate-green" [ngClass]="(displayLoginSection === 3?'small-login-panel':'')"> -->
    <div class="login-panel" [ngClass]="(displayLoginSection === 3?'small-login-panel':'')">
      <h2 text-center style="color: white;" margin-top margin-bottom> Sign in to Dashboard</h2>
      <div class="lunchMate-logo-div">
        <!-- <img class="lunchMate-logo" src="assets/images/lunchPos.png"> -->
        <img class="logo" src="assets/imgs/lunchmate_white_logo.png">
      </div>

      <div *ngIf="displayLoginSection===1" class="first-panel ion-margin-bottom">  
        <form #login_form="ngForm" ng-submit="loginBtnClick()" novalidate>
          <ion-list>
            <ion-item>
              <ion-label floating>Username</ion-label>
              <ion-input (keyup.enter)="loginClick()" name="username" type="text" value="" [(ngModel)]="username"
                required></ion-input>
            </ion-item>
  
            <ion-item>
              <ion-label floating>Password</ion-label>
              <ion-input (keyup.enter)="loginClick()" name="password" type="password" [(ngModel)]="password" required>
              </ion-input>
            </ion-item>
          </ion-list>
          <ion-button style="margin-bottom: 10px;" type="submit" (click)="loginClick()" [disabled]="!login_form.valid"
            color="success">Log
            in</ion-button>
        </form>
      </div>

      <div *ngIf="displayLoginSection === 2">
        <p class="rest-name">
          Restaurant Name: {{lastLoggedRestDetails?.rest_name}}
        </p>
        <div class="user-list-panel">
          <div color="lunchmate-green" text-center class="user-text">
            Select a Username
          </div>
          <ion-list *ngIf="usersList.length" style="max-height: 150px;
        overflow: auto">
  
            <ion-item *ngFor="let user of usersList" (click)="userClicked(user)">
              <!-- {{globalService.userName}} -->
              {{user.user_full_name}}
              <ion-icon name="caret-forward" slot="end" color="lunchmate-green" float-right> </ion-icon>
            </ion-item>
          </ion-list>
          <div class="ion-text-end ion-padding">
            <a color="lunchmate-green" class="another-user-link" (click)="anotherClick()">Login as different user</a>
          </div>
        </div>
        <div class="ion-text-center ion-padding version-row">
          <div>v {{appVersion}}</div>
        </div>
      </div>

      <div *ngIf="displayLoginSection === 3" class="user-list-panel">
        <div color="lunchmate-green" text-center class="user-text">
          User '{{selectedUser.user_full_name}}'
        </div>
        <ion-label class="" margin-top>
          Enter {{selectedUser.login_pin ? '4-digit PIN' : 'Password'}}
        </ion-label>
        <form (submit)="loginClick(selectedUser)">
          <ion-input #passwordInput class="border-input" *ngIf="!selectedUser.login_pin" name="password" type="password" [(ngModel)]="password"
            class="border-input" margin-bottom></ion-input>
          <ion-input class="border-input" *ngIf="selectedUser.login_pin" name="login_pin" type="text" pattern="[0-9]*"
            [(ngModel)]="login_pin" inputmode="numeric" maxlength="4" style="-webkit-text-security: disc;"
            class="border-input" margin-bottom #pinInput (keyup)="pinInputKeyUp(selectedUser, pinInput.value)"></ion-input>
          <div>
            <ion-button small type="submit" [disabled]="!password && !login_pin" color="success">Log
              in</ion-button>
          </div>
        </form>
        <div class="ion-text-center ion-padding">
          <a color="lunchmate-green" class="another-user-link" (click)="anotherClick()">Login as different user</a>
        </div>
      </div>
      <div  *ngIf="displayLoginSection !== 2" class="ion-text-center ion-padding version-row">
        <div>v {{appVersion}}</div>
      </div>
    </div>
  

  <!-- </div> -->
</ion-content>
