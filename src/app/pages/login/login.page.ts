import { ToastService } from '../../_providers/toast.service';
import { LoadingService } from '../../_providers/loading.service';
import { ApisService } from '../../_providers/api.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { GlobalService } from '../../_providers/global.service';
import { EventsService } from 'src/app/_providers/events.service';
import { Storage } from '@ionic/storage';
import { AlertController, LoadingController } from '@ionic/angular';
import { forkJoin } from 'rxjs';
import { APP_CONFIG } from 'src/app/_configs/app.config';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {

  @ViewChild('pinInput', { static: false }) myInput;
  @ViewChild('passwordInput', { static: false }) myPassInput;
  username;
  password;
  staySignedIn = true;
  userDetails;
  isUserLoggedIn = true;
  login_pin;
  selectedUser;
  displayLoginSection = 2;
  usersList: any = [];
  lastLoggedRestDetails: any;
  appVersion: any;

  constructor(
    private apisService: ApisService,
    private loadingCtrl: LoadingController,
    private toastService: ToastService,
    private router: Router,
    private globalService: GlobalService,
    private events: EventsService,
    private storage: Storage,
    private alertController: AlertController
  ) { }

  ngOnInit() {
  }

  async ionViewDidEnter() {
    this.appVersion = APP_CONFIG.APP_VERSION;
    let loader = await this.loadingCtrl.create({
      message: "Checking configuration..."
    });
    loader.present();
    this.getBaseUrl().then(
      response => {
        this.getStorageVariables(loader);
        let post_data = {};

        this.proceedFurther(loader);
        // this.apisService.checkIfLocationUpdated(post_data).subscribe(successResponse => {
        //   console.log('checkIfLocationUpdated ' + successResponse);
        //   // loader.dismiss();
        //   if (successResponse.isSuccess) {
        //     this.proceedFurther(loader);
        //   }
        //   else {
        //     if (successResponse.errType == 'LOCATION_UPDATED') {
        //       // console.log('LOCATION_UPDATED');
        //       this.apisService.setBaseUrl(successResponse.updatedLocation);
        //       this.storage.set('baseUrlDashboard', successResponse.updatedLocation);
        //     }
        //     this.proceedFurther(loader);
        //   }
        // },
        //   err => {
        //     console.error(err);
        //     // loader.dismiss();
        //     this.proceedFurther(loader);
        //   });
      }
    );
    this.getUsersList();
  }

  async loginClick(user = null) {
    let loader = await this.loadingCtrl.create({
      message: "Logging in..."
    });
    loader.present();

    let post_data;
    if (user){
      console.log('####user', user);
      console.log('####pin', this.login_pin);

      if(user.login_pin) {
        post_data = {
          uid: user.user_id,
          // un: this.username,
          un: null,
          pw: null,
          login_pin: this.login_pin
        };
      } else {
        console.log('####user', user);
        post_data = {
          uid: user.user_id,
          un: this.username,
          pw: this.password
        };
      }
    } else {
      post_data = {
        uid: null,
        un: this.username,
        pw: this.password
      };
    }
    this.apisService.login(post_data).subscribe((res: any) => {
        console.log(res);
        if (res.isSuccess) {
          // this.toastService.toastServices('Logged In Successfully', 'success', 'top');
          if (localStorage.getItem('loginName') != this.globalService.userName) {
            console.log('user changed - old orders will be removed!');
            this.storage.remove('posOrdersList');
          }
          this.globalService.userName = this.username;
          this.globalService.user_id = res.user_id;
          this.globalService.rest_id = res.assignedRestId;
          this.globalService.rest_details = res.rest_details;
          this.globalService.user_full_name = res.user_full_name;
          this.globalService.user_first_name = res.user_first_name;
          this.globalService.user_email = res.user_email;
          this.apisService.setBearer(res.access_token);
          localStorage.setItem('loginName', this.globalService.userName);
          console.log('locstorage UserName',this.globalService.userName);
          // this.storage.set("lastLoggedRest", this.globalService.rest_details);
          this.storage.set("lastLoggedRest", res.assigned_rest_details);

          // this.storage.set('tradingStarted', this.globalService.tradingStarted);
          this.globalService.access_token = res.access_token;
          this.globalService.assignedRestDetails = res.assigned_rest_details;
          this.getRestaurantStatus(this.globalService.assignedRestDetails.rest_id);
          this.globalService.userDetails = {
            username: this.username,
            password: this.password
          };
          if (this.staySignedIn) {
            this.storage.set("userDetailsDashboard", this.globalService.userDetails);
          }
          setTimeout(() => {
            this.globalService.allLoadingDone = true;
            this.globalService.setSleepMode();
            this.router.navigateByUrl('food-orders');
          }, 0);
          // localStorage.setItem('token', res.access_token);
          // localStorage.setItem('assignedRestDetails', JSON.stringify(res.assigned_rest_details));
          // localStorage.setItem('userDetails', JSON.stringify({
          //   username: this.username,
          //   password: this.password
          // }))

        } else {
          // alert(res.message);
          this.presentErrorAlert(res.message);
          this.password = '';
        }
       loader.dismiss();
     },
       err => {
         console.error(err);
         loader.dismiss();
         this.presentErrorAlert(this.apisService.commonNetworkErrorMessage);
        //  alert(this.apisService.commonNetworkErrorMessage);
       });
  }

  async presentErrorAlert(errorMessage: string) {
    const alert = await this.alertController.create({
      header: 'Error',
      message: errorMessage,
      buttons: ['OK'],
    });

    await alert.present();
  }

  getRestaurantStatus(rest_id: any) {
    let post_data = {
      rest_id: rest_id,
      user_id: this.globalService.assignedRestDetails.rest_admin_id
    };
    //   console.log(post_data);

    this.apisService.getRestaurantStatusForDashboard(post_data).subscribe(
      (response: any) => {
        //  console.log(response);
        if (response.isSuccess) {
          //    console.log(response.responseData.last_connected_at);
          this.events.publish("set:last_connected", response.responseData.last_connected_at);
          setTimeout(() => {
            this.getRestaurantStatus(rest_id);
          }, 10000);
        } else {
          console.error(response.message);
          setTimeout(() => {
            this.getRestaurantStatus(rest_id);
          }, 10000);
        }
      },
      err => {
        console.error(err);
        setTimeout(() => {
          this.getRestaurantStatus(rest_id);
        }, 10000);
      }
    );
  }

  getBaseUrl(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.storage.get('baseUrlDashboard').then(baseUrl => {
        if (!baseUrl) {
          baseUrl = APP_CONFIG.API_URL;
        }
        else {
          // baseUrl = baseUrl;
          baseUrl = APP_CONFIG.API_URL;
        }
        this.apisService.setBaseUrl(baseUrl);
        this.storage.set('baseUrlDashboard', baseUrl);
        resolve('retrieved baseUrl');
      });
    });
  }

  getStorageVariables(loader) {
    forkJoin([
      this.storage.get("appSettingsDashboard"),
      this.storage.get("availablePrintersDashboard"),
      this.storage.get("userDetailsDashboard")
    ]).subscribe(
      resultArray => {
        if (resultArray[0]) {
          var appSettings = resultArray[0];
          this.globalService.appSettings.currentDisplayType =
            'display'; //appSettings.currentDisplayType;
          // alert('result 0 - ' + appSettings.currentDisplayType);
          this.globalService.appSettings.isTrackingAutomatically = appSettings.isTrackingAutomatically;
          // this.globalService.appSettings.isLabelPrintingAutomatically =
          //   appSettings.isLabelPrintingAutomatically;
          this.globalService.appSettings.selectedPrinterIndex = appSettings.selectedPrinterIndex;
        }

        if (resultArray[1]) {
          this.globalService.availablePrinters = resultArray[1];
        }

        if (resultArray[2]) {
          this.globalService.userDetails = resultArray[2];
          // if (this.globalService.userDetails.token) {
          //   this.apisService.setBearer(this.globalService.userDetails.token);
          // }
        }

        // if (this.globalService.appSettings.currentDisplayType) {
        //   // alert('from storage - ' + this.globalService.appSettings.currentDisplayType);
        //   this.globalService.changeScreenOrientation(this.globalService.appSettings.currentDisplayType);
        // } else {
        //   if (this.globalService.isDeviceTablet) {
        //     // alert('tablet');
        //     this.globalService.changeScreenOrientation('display');
        //   }
        //   else {
        //     // alert('no tablet');
        //     this.globalService.changeScreenOrientation('dashboard');
        //   }
        //   this.storage.set("appSettingsDashboard", this.globalService.appSettings);
        // }


        loader.dismiss();
        // this.storageRetrieved = true;
        setTimeout(() => {
          console.log(this.globalService.userDetails);
          console.log(this.globalService.availablePrinters);
          console.log(this.globalService.appSettings);

          if (this.globalService.userDetails) {
            if (this.globalService.userDetails.username && this.globalService.userDetails.password) {
              this.username = this.globalService.userDetails.username;
              this.password = this.globalService.userDetails.password;
              this.loginClick();
            }
          }
        }, 10);

        // if (this.globalService.appSettings.activeRestaurantId) {
        //   var restaurantIndex = this.globalService.addedRestaurants.findIndex(
        //     restaurant =>
        //       restaurant.rest_id ==
        //       this.globalService.appSettings.activeRestaurantId
        //   );
        //   if (restaurantIndex != -1) {
        //     this.globalService.activeRestaurant = this.globalService.addedRestaurants[
        //       restaurantIndex
        //     ];
        //     this.globalService.appSettings.isDemoMode = this.globalService.activeRestaurant.is_test_type == 1;
        //   }
        // }

      },
      err => {
        console.error(err);
      }
    );
  }

  quitClick() {
  // if (this.plt.is('cordova')) {
  // this.plt.exitApp();
  //}
  }

  getUsersList() {
    // if (1) {
    //   this.usersList = ["Lorem pinto", "Sakina yhei", "Joen Doe"];
    //   return this.usersList;
    // }
    //// get last active rest from local

    this.storage.get('lastLoggedRest').then(lastLoggedRest => {
      console.log('last logged restaurant:',lastLoggedRest);
      if (lastLoggedRest) {
        this.lastLoggedRestDetails = lastLoggedRest;
        this.getRestUserFromServer();
        this.displayLoginSection = 2;
      } else {
        this.usersList = [];
        this.displayLoginSection = 1;
        this.username = '';
        this.password = '';
      }

    });
  }


  getRestUserFromServer() {
    // let loader = this.loadingCtrl.create({
    //   content: "Getting users..."
    // });
    // loader.present();
    console.log('lastLoggedRestDetails', this.lastLoggedRestDetails);

    let post_data = {
      rest_id: this.lastLoggedRestDetails.rest_id,
    };

    this.apisService.getRestUsers(post_data).subscribe((response: any) => {
      console.log(response);
      if (response.isSuccess) {
        this.usersList = response.rest_users;
      } else {
        // this.alertsServices.presentAlert(null, response.resultRes);
      }
      // loader.dismiss();
    },
      err => {
        // loader.dismiss();
        console.error(err);
        // this.alertsServices.presentAlert(null, this.apiServices.commonNetworkErrorMessage);
      });
  }

  proceedFurther(loader) {
    this.username = '';
    this.password = '';
    //
    if (localStorage.getItem("loginName") != null) {
      console.log()
      this.username = localStorage.getItem("loginName");
    }

    // if (1) {
    this.getUsersList();
    // }
    //
  }


  pinInputKeyUp(selectedUser, val) {
    // console.log({ val });
    if (val.length >= 4) {
      this.loginClick(selectedUser);
    }
  }


  userClicked(user) {
    console.log({ user });
    this.displayLoginSection = 3;
    this.selectedUser = user;
    if(this.displayLoginSection === 3) {
      setTimeout(() => {
        if(this.selectedUser.login_pin) {
          this.myInput.setFocus();
        } else if (!this.selectedUser.login_pin) {
          this.myPassInput.setFocus();
        }
      },150);
    }
  }

  anotherClick() {
    this.username = '';
    this.password = '';
    if (this.displayLoginSection === 2) {
      this.displayLoginSection = 1;

    } else {
      this.displayLoginSection = 2;
    }
  }


}
