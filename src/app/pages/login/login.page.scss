//   .login-board {
//       margin: 0 auto;
//       padding: 20px;
//       margin-top: 50px;
//       width: 50%;
//       min-width: 320px;
//       background-color: var(--ion-color-primary);
//       //   background-color: color($colors, lunchmate-green);
//       border-radius: 10px;
//       padding-bottom: 50px;
//   }
  .logo {
      width: 70%;
      padding: 3%;
  }
  .item.item-md .checkbox-md {
      margin-right: 9px;
  }
//   .centered {
//     position: fixed;
//     top: 50%;
//     left: 50%;
//     transform: translate(-50%, -50%);
//   }

    $win-height: 100%;
    $block-a-height: $win-height*1/2;
    $lm-green: #3da735;
  
    .login-title {
      text-align: center;
    }
  
    .lunchMate-logo-div {
      height: $block-a-height;
      // background-color: color($colors, lunchmate-green, base);
      text-align: center;
  
  
    }
  
    .first-panel {
      // margin-bottom: 15%;
      border-radius: 5px;
      width: 60%;
      /* align-items: center; */
      left: 20%;
      padding-top: 2%;
      margin-bottom: 3%;
      /* justify-content: space-between; */
      position: relative;
      background: white;
    }
  
    .login-panel {
    //   background-color: color($colors, lunchmate-green, base);
      width: 60%;
      background-color: var(--ion-color-lunchmate-green);
      text-align: center;
      position: absolute;
      border-radius: 14px;
      top: 5%;
      left: 20%;
      min-height: 350px;
      max-height: 580px;
  
    }
  
    .small-login-panel {
      width: 50;
      width: 45%;
      min-height: 380px;
      left: 25%;
    }
  
    .lunchMate-logo {
      width: 65%;
      margin: 40px 0px 30px 0px; //   height:  $win-height;
      // max-width: 100%;
      /*background-image: url(../../assets/images/lunchMate.png);*/
    }
  
    .user-list-panel {
      width: 60%;
      left: 20%;
      position: relative;
      background: white;
      padding-top: 5px;
      // margin-bottom: 15%;
      border-radius: 5px;
  
      .user-text {
        font-size: 20px;
        margin: 10px 10px;
        color: var(--ion-color-lunchmate-green);
        font-weight: bold;
      }
    }
  
    // .item-inner {
    //   border-bottom: 1px solid color($colors, lunchmate-green, base) !important;
    // }
  
    .another-user-link {
  
    //   color: color($colors, lunchmate-green, base);
    //   ;
      text-decoration: underline;
    }
  
    .rest-name {
      color: white;
      font-size: 20px;
      font-weight: 600;
    } 

    .border-input {
      margin-top: 5px;
      margin-bottom: 5px;
      left: 30%;
      width: 40%;
      height: 30px;
      border: 2px solid #8c8c8c;
      border-radius: 5px;
    }

    .version-row {
      display: flex;
      justify-content: center;
      margin-top: 10px;
      font-size: small;
      color: white;
      bottom: 0;
    }