import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';


@Injectable({
  providedIn: 'root'
})
export class AuthGaurd implements CanActivate {
  token: any;
  constructor(private router: Router){}
  canActivate(): boolean {
    this.token = localStorage.getItem('token');
    if (!this.token) {
      this.router.navigateByUrl('/login');
      return false;
    }
    return true;
  }

}
