import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SideMenuComponent } from './components/side-menu/side-menu.component';
import { HeaderComponent } from './components/header/header.component';

@NgModule({
  declarations: [
    SideMenuComponent,
    HeaderComponent
  ],
  imports: [CommonModule, IonicModule, ReactiveFormsModule, FormsModule, RouterModule],
  exports: [
    SideMenuComponent,
    HeaderComponent
  ],
  schemas: [
    NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA
  ],
})
export class ComponentsModule {
}
