import { Component } from '@angular/core';
import { EventsService } from './_providers/events.service';
import { GlobalService } from './_providers/global.service';
import { Platform } from '@ionic/angular';
import { Network } from '@capacitor/network';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent {
  // private statusBar: StatusBar, private splashScreen: SplashScreen
  isAppOnline: boolean = true;

  constructor(private platform: Platform, private events: EventsService, private globalService: GlobalService) {
    this.initializeApp();
  }

  initializeApp() {
    this.platform.ready().then(() => {
      // this.statusBar.styleDefault();
      // this.statusBar.backgroundColorByHexString("#ffffff");
      // this.splashScreen.hide();
      this.globalService.getSettingsFromStorage();
      this.globalService.configSleepMode();
      // this.globalService.configSleepMode();
      this.initializeNetworkEvents();
    });
  }

  async initializeNetworkEvents() {
    // Get initial status
    const status = await Network.getStatus();
    this.isAppOnline = status.connected;

    // Listen for network status changes
    Network.addListener('networkStatusChange', (status) => {
      this.isAppOnline = status.connected;
      console.log('Network status changed', status.connected);
    });
  }

}
