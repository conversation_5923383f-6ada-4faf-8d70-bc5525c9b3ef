import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-voucher',
  templateUrl: './voucher.page.html',
  styleUrls: ['./voucher.page.scss'],
})
export class VoucherPage implements OnInit {

  @ViewChild("voucherImage", {
    read: ElementRef
  })
  voucherImage;
  voucher = {
    token_code: null,
    token_image_base64: null,
    token_name: null,
    amount: null
  };

  constructor(private viewCtrl: ModalController) { }

  ngOnInit() {
  }

  ionViewDidEnter() {
    // console.log('ionViewDidLoad VoucherPage');
    // this.menuCtrl.swipeEnable(false);
  }

  ionViewDidLeave() {
    // this.menuCtrl.swipeEnable(true);
  }

  printClick() {
    let voucherImageElement = this.voucherImage.nativeElement;
    var canvas = document.createElement("canvas");
    let originalWidth = voucherImageElement.width;
    let originalHeight = voucherImageElement.height;
    // we will change to width 300px, height will set relatively
    let newWidth = 300;
    let newHeight = Math.floor(
      (newWidth / originalWidth) * originalHeight
    );
    //
    canvas.width = newWidth;
    canvas.height = newHeight;
    var ctx = canvas.getContext("2d");
    ctx.drawImage(voucherImageElement, 0, 0, newWidth, newHeight);
    this.viewCtrl.dismiss({
      canvasContext: ctx
    });
  }

  close() {
    this.viewCtrl.dismiss();
  }

}
