<ion-header>
  <ion-toolbar color="primary">
    <ion-title>Voucher - {{voucher.token_code}}</ion-title>
    <ion-buttons slot="start" class="ion-padding">
      <ion-button (click)="close()" fill="clear">
        <ion-icon class="back-icon" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding ion-text-center">
  <div class="text-center">
    <h2>{{voucher.token_code}}</h2>
    <img *ngIf="voucher.token_image_base64" #voucherImage class="token-image"
      src="{{'data:image/png;base64,' + voucher.token_image_base64}}">
    <div *ngIf="!voucher.token_image_base64">Could not load voucher image. File might have been moved or deleted on the
      server.</div>
  </div>
  <div class="ion-padding-top">
    <strong>Name: </strong><span>{{voucher.token_name}}</span>
  </div>
  <div class="ion-padding-top ion-padding-bottom">
    <strong>Value: </strong><span>{{voucher.amount | currency:'GBP'}}</span>
  </div>
  <ion-button (click)="printClick()" [disabled]="!voucher.token_image_base64" fill="solid">
    Print
  </ion-button>
</ion-content>