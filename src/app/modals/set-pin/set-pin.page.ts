import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { AlertsServices } from 'src/app/_providers/alert.service';

@Component({
  selector: 'app-set-pin',
  templateUrl: './set-pin.page.html',
  styleUrls: ['./set-pin.page.scss'],
})
export class SetPinPage implements OnInit {

  pin;
  c_pin;

  constructor(private viewCtrl: ModalController, private alertsServices: AlertsServices) { }

  ngOnInit() {
  }

  close() {
    this.viewCtrl.dismiss();
  }

  okClick() {
    if (/^\d+$/.test(this.pin)) {
      this.viewCtrl.dismiss(this.pin);
    } else {
      this.alertsServices.presentAlert(null, 'Invalid value. Please enter a 4-digit PIN.');
    }
  }


}
