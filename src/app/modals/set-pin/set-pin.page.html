<ion-header>
  <ion-toolbar color="primary">
    <ion-title>Set your PIN</ion-title>
    <ion-buttons slot="end" class="ion-padding">
      <ion-button (click)="close()" fill="clear">
        <ion-icon class="cross-icon" name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding">
  <ion-item>
    <ion-label position="stacked"> Enter new 4-digit PIN</ion-label>
    <ion-input type="text" pattern="[0-9]*" inputmode="numeric" maxlength="4" style="-webkit-text-security: disc;"
      name="pwd" [(ngModel)]="pin" required>
    </ion-input>
  </ion-item>
  <ion-item>
    <ion-label position="stacked">Confirm new 4-digit PIN </ion-label>
    <ion-input type="text" pattern="[0-9]*" inputmode="numeric" maxlength="4" style="-webkit-text-security: disc;"
      name="cpwd" [(ngModel)]="c_pin" required></ion-input>
  </ion-item>
  <small *ngIf="pin &&c_pin && (pin!= c_pin)" padding-left> Both fields do not match</small>
</ion-content>
<ion-footer>
  <ion-toolbar>
    <div class="ion-text-end">
      <ion-button (click)="close()" class="btn-footer" color="light" fill="solid">
        Cancel
      </ion-button>
      <ion-button class="btn-footer" color="primary" (click)="okClick()" [disabled]="!pin || (pin!= c_pin)">
        Ok
      </ion-button>
    </div>
  </ion-toolbar>
</ion-footer>