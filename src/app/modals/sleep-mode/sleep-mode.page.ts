import { Component, OnInit } from '@angular/core';
import { ModalController, NavController, NavParams } from '@ionic/angular';
import { GlobalService } from 'src/app/_providers/global.service';

@Component({
  selector: 'app-sleep-mode',
  templateUrl: './sleep-mode.page.html',
  styleUrls: ['./sleep-mode.page.scss'],
})
export class SleepModePage implements OnInit {

  logged_user_full_name = null;

  constructor(private navCtrl: NavController, public navParams: NavParams, private viewCtrl: ModalController, private globalService: GlobalService) {
  }

  ngOnInit() {

  }

  ionViewWillEnter() {
    console.log('ionViewWillEnter SleepModeModalPage');
    this.logged_user_full_name = this.navParams.get("user_full_name");
  }

  close() {
    this.viewCtrl.dismiss()
  }

}
