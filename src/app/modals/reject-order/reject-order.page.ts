import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-reject-order',
  templateUrl: './reject-order.page.html',
  styleUrls: ['./reject-order.page.scss'],
})
export class RejectOrderPage implements OnInit {

  rejectMessage = '';

  constructor(private modalController: ModalController) { }

  ngOnInit() {
    this.rejectMessage = 'We regret to inform you that your order has been rejected.';
  }

  rejectClick() {
    this.modalController.dismiss(this.rejectMessage);
  }

  cancelClick() {
    this.modalController.dismiss();
  }

}
