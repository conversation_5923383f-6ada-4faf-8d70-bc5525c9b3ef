<!-- <ion-header>
  <ion-toolbar>
    <ion-title>reject-order</ion-title>
  </ion-toolbar>
</ion-header> -->

<ion-content>
  <ion-grid>
    <ion-row class="ion-padding">
      <ion-col size="2">
          <!-- <ion-icon name="ios-chatbubbles-outline" class="chat-bubbles"></ion-icon>
          <div class="heading-text">
              send a message with this order rejection
          </div> -->
          <ion-icon class="chat-icon" name="chatbubbles-outline" item-start></ion-icon>
         


              <!-- <div class="prepared-message">
                  <ion-icon name="ios-chatbubbles-outline" class="chat-bubbles"></ion-icon>
                  <div class="heading-text">
                    Send message with prepared orders
                  </div>
                  
                  <textarea></textarea>
              </div> -->
         
      </ion-col>
      <ion-col size="10">
          <span class="heading-txt" text-wrap> Send a message with this order rejection<sup>*</sup></span>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col size="12">
        <textarea [(ngModel)]="rejectMessage" placeholder="Rejection message"></textarea>
      </ion-col>
    </ion-row>
    <ion-row>
      <!-- <ion-col size="12">
        <ion-text color="medium">* Any loyalty stamp that the user has acquired for this order will remain.</ion-text>
      </ion-col> -->
      <ion-col>
        <ion-text style="color:white;">* Any loyalty stamp that the user has acquired for this order will remain. </ion-text>
        </ion-col>
      </ion-row>
    <ion-row>
        <ion-col class="ion-text-center" size="6">
            <ion-button class="custom-btn" (click)="cancelClick()"  fill="clear" color="light"> Cancel</ion-button>
        </ion-col>
        <ion-col text-center size="6">
            <!-- <ion-button [disabled]="!rejectMessage" class="custom-btn" (click)="rejectClick()"  color="rejBtn">Reject Order</ion-button> -->
            <button [disabled]="!rejectMessage" class="custom-btn" (click)="rejectClick()" style="color: red; background-color: white; border-radius: 5px;">Reject Order</button>
        </ion-col>
        <!-- <ion-col size="6">
          <ion-button class="qwer" color="light">Reject Order</ion-button>
        </ion-col> -->
    </ion-row>
  </ion-grid>
</ion-content>
