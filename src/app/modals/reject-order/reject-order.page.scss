ion-content{
    --background: red;
}

.heading-txt {
    font-size: 24px;
    // font-weight: bold;
    padding-left: 5px;
    color: white;
}

textarea {
    margin-top: 10px;
    width: 100%;
    height: 120px;
    resize: none;
    background: white;
}

.chat-icon {
    font-size: 48px;
    padding: 8px 0px 0px 0px;
    color: white;
    font-weight: bold;
}

.custom-btn {
    text-transform: none;
    height: 45px;
    font-size: 18px;

}
// .custom-btn-cancel {
//     text-transform: none;
//     height: 45px;
//     font-size: 18px;

// }

// .custom-btn-reject {
//     text-transform: none;
//     height: 45px;
//     font-size: 18px;
//     color: red;

// }




// .prepared-message{
//     padding:10px;
//     height:200px;
//     width: 100%;
//     background-color: color($colors, lunchmate-green);
//     position: absolute;
//     bottom:0;
//     border-radius:6px 6px 0px 0px;
// }

// .prep-msg-closed{
//     height:46px;
// }

// .prepared-message .heading-text{
//     margin-left:10px;
//     color:white;
//     font-weight:800;
//     width:70%;
//     float:left;
// }

// .prepared-message .chat-bubbles{
//     color:white;
//     font-size:2em;
//     float:left;
//     font-weight: 800;
//     margin-left: 8px;
// }

// .prepared-message .showhide-button{
//     color: white;
//     width: 15%;
//     font-size: 1.2em;
//     margin-top: -4px;
//     float:right;
// }

// .prepared-message textarea{
//     margin-top:10px;
//     width: 100%;
//     height:120px;
//     resize: none;
// }


$modal-reject-display-width: 550px !default;
$modal-reject-display-height: 340px !default;
$modal-reject-display-border-radius: 12px !default;
//
$modal-reject-dashboard-width: 300px !default;
$modal-reject-dashboard-height: 400px !default;
$modal-reject-dashboard-border-radius: 12px !default;

ion-modal.reject-modal-display .modal-wrapper {
    z-index: 10;
    height: 100%;
    contain: strict;

    // @media only screen and (min-width: $modal-inset-min-width) and (min-height: $modal-inset-min-height-small) {
    //     @include position(calc(50% - (#{$modal-reject-display-height}/ 2)), null, null, calc(50% - (#{$modal-reject-display-width}/ 2)));
    //     @include border-radius($modal-reject-display-border-radius);
    //     position: absolute;
    //     width: $modal-reject-display-width;
    //     height: $modal-reject-display-height;
    // }

    // @media only screen and (min-width: $modal-inset-min-width) and (min-height: $modal-inset-min-height-large) {
    //     @include position(calc(50% - (#{$modal-reject-display-height}/ 2)), null, null, calc(50% - (#{$modal-reject-display-width}/ 2)));
    //     position: absolute;
    //     width: $modal-reject-display-width;
    //     height: $modal-reject-display-height;
    // }
    @media only screen and (min-width: 0) and (min-height: 0) {
        position: absolute;
        width: $modal-reject-display-width;
        height: $modal-reject-display-height;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        border-radius: $modal-reject-display-border-radius;
    }
}

ion-modal.reject-modal-dashboard .modal-wrapper {
    z-index: 10;
    height: 100%;
    contain: strict;

    @media only screen {
        position: absolute;
        width: $modal-reject-dashboard-width;
        height: $modal-reject-dashboard-height;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        border-radius: $modal-reject-dashboard-border-radius;
    }

    // @media only screen {
    //     @include position(calc(50% - (#{$modal-reject-dashboard-height}/ 2)), null, null, calc(50% - (#{$modal-reject-dashboard-width}/ 2)));
    //     @include border-radius($modal-reject-dashboard-border-radius);
    //     position: absolute;
    //     width: $modal-reject-dashboard-width;
    //     height: $modal-reject-dashboard-height;
    // }

// @media only screen and (min-width: $modal-inset-min-width) and (min-height: $modal-inset-min-height-small) {
//   @include position(calc(50% - (#{$modal-reject-dashboard-height}/ 2)), null, null, calc(50% - (#{$modal-reject-dashboard-width}/ 2)));
//   @include border-radius($modal-reject-dashboard-border-radius);
//   position: absolute;
//   width: $modal-reject-dashboard-width;
//   height: $modal-reject-dashboard-height;
// }

// @media only screen and (min-width: $modal-inset-min-width) and (min-height: $modal-inset-min-height-large) {
//   @include position(calc(50% - (#{$modal-reject-dashboard-height}/ 2)), null, null, calc(50% - (#{$modal-reject-dashboard-width}/ 2)));
//   position: absolute;
//   width: $modal-reject-dashboard-width;
//   height: $modal-reject-dashboard-height;
// }
}