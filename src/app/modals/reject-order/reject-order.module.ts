import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RejectOrderPageRoutingModule } from './reject-order-routing.module';

import { RejectOrderPage } from './reject-order.page';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RejectOrderPageRoutingModule
  ],
  declarations: [RejectOrderPage]
})
export class RejectOrderPageModule {}
