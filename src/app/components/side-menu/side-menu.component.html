<ion-toolbar class="logo-toolbar" [ngStyle]="{'background-color':globalService.assignedRestDetails?.header_bar_color}">
  <ion-title text-center>
    <!-- Iamge Here -->
    <img class="rest-logo-display" src="{{globalService.assignedRestDetails?.rest_logo}}">
  </ion-title>
</ion-toolbar>

<ion-list class="menu-list">
  <ng-container *ngFor=" let menuItem of menuItems">
    <ion-item button (click)="menuItemClick(menuItem)">
      <ion-icon name="{{menuItem.icon}}" slot="start">
        <i *ngIf="menuItem.icon == 'log-out-outline'" class="lm-icon-sign_out icon"></i>
      </ion-icon>
      {{menuItem.title}}
      <ion-icon name="checkmark" slot="end" *ngIf="globalService.currentPage == menuItem.title">
      </ion-icon>
    </ion-item>
  </ng-container>
</ion-list>

<div class="rest-detail-dashboard custom-text-color">
  <div>Today's Total: {{globalService.todaysTotalAmount | currency:'GBP'}}</div>
  <div>
    <div class="version-info">v {{version}}</div>
  </div>
</div>

<!-- <div class="prepared-message" [ngClass]="{'prep-msg-closed': !bMessagePanelVisible}">
  <ion-row>
    <ion-col>
      <ion-icon name="ios-chatbubbles-outline" class="chat-bubbles"></ion-icon>
      <div class="heading-text">
        Send message with prepared orders
      </div>
      <div class="showhide-button" (click)="showHideMessagePanelClick()">
        <ion-icon *ngIf="!bMessagePanelVisible" name="arrow-dropup-circle"></ion-icon>
        <ion-icon *ngIf="bMessagePanelVisible" name="arrow-dropdown-circle"></ion-icon>
      </div>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <textarea [(ngModel)]="globalService.processedMessage"></textarea>
    </ion-col>
  </ion-row>
</div> -->
