
.logo-toolbar {
    // height: 65px;
    // margin-top: 8px;

    .toolbar-background {
      background: transparent !important;
    }
  }

  ion-list {
    background-color: transparent;
    ion-item::part(native){
      background-color: transparent;
      color: white;
      padding-left: 0;
      .item-inner{
        border-bottom:1px solid white;
      }
    }
    .item {
      padding-left: 4px;
      background-color: transparent;
      ion-icon {
        // margin-right: 20px !important;
        color:white;
      }
    }
  }

  .rest-logo-display {
    // max-width: 90%;
  }

  .prepared-message {
    padding: 10px;
    height: 200px;
    width: 100%;
    background-color: var(--ion-color-lunchmate-green);//color($colors, lunchmate-green);
    position: absolute;
    left: 0px;
    bottom: 0px;
    border-radius: 8px 8px 0px 0px;
    overflow: hidden;
  }

  .prep-msg-closed {
    height: 40px;
    overflow: hidden;
    padding-top: 0px
  }

  .prepared-message .chat-bubbles {
    color: white;
    font-size: 2em;
    float: left;
    font-weight: 800;
    // margin-left: 8px;
    margin-left: -8px;
  }

  .prepared-message .heading-text {
    // margin-left: 10px;
    margin-left: 5px;
    color: white;
    font-weight: 800;
    width: 70%;
    float: left;
    //
    font-size: 1vw;
  }

  .prepared-message .showhide-button {
    color: white;
    // width: 15%;
    font-size: 1.6em;
    // margin-top: -38px;
    float: right;
    //
    // margin-top: -12px;
    margin-right: -8px;
    // margin-bottom: 0px;
  }

  .prepared-message textarea {
    margin-top: 10px;
    width: 100%;
    height: 120px;
    resize: none;
    //
    margin-top: 0px;
  }

  .rest-detail-dashboard {
    font-weight: bold;
    font-size: 16px;
    position: absolute;
    bottom: 10px;
    left: 0;
    text-align: center;
    right: 0;
    // color: white;
    .version-info{
      font-size:smaller;
      font-weight:normal;
      margin-top:10px;
      font-style:oblique;
    }
  }

  .rest-detail-dashboard-panel-closed {
    bottom: 56px;
  }
  .menu-list{
    overflow: auto;
    max-height: calc(100% - 120px);
  }