import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Storage } from '@ionic/storage';
import { APP_CONFIG } from 'src/app/_configs/app.config';
import { EventsService } from 'src/app/_providers/events.service';
import { GlobalService } from 'src/app/_providers/global.service';

@Component({
  selector: 'app-side-menu',
  templateUrl: './side-menu.component.html',
  styleUrls: ['./side-menu.component.scss'],
})
export class SideMenuComponent implements OnInit {

  menuItems: Array<{ title: string, icon: string }>;
  text: string;
  bMessagePanelVisible = false;
  version;

  constructor(private events: EventsService, public globalService: GlobalService, private navCtrl: NavController, private storage: Storage) {
    // console.log('Hello DisplaySideMenuComponent Component');
    this.text = 'Hello World';

    this.menuItems = [
      { title: "Orders", icon: "menu" },
      { title: "Menus", icon: "apps" },
      { title: "Vouchers", icon: "heart" },
      { title: "Settings", icon: "settings" },
      { title: "Logout", icon: "log-out-outline" },
    ];

  }

  ngOnInit() {
    this.version = APP_CONFIG.APP_VERSION;
  }

  menuItemClick(menuItem) {
    console.log(menuItem.title + " clicked!!");
    switch (menuItem.title) {
      case "Food Orders":
      case "Orders":
        // this.events.publish('foodorders:clicked', {});
        this.navCtrl.navigateRoot(['food-orders']);
        break;
      case "Food Menus":
      case "Menus":
        // this.events.publish('foodmenus:clicked', {});
        this.navCtrl.navigateRoot(['food-menus']);
        break;
      case "Vouchers":
        // this.events.publish('vouchers:clicked', {});
        this.navCtrl.navigateRoot(['vouchers']);
        break;
      case "Settings":
        // this.events.publish('settings:clicked', {});
        this.navCtrl.navigateRoot(['settings']);
        break;
      case "Logout":
        // this.events.publish('signout:invoked', {});
        this.globalService.allLoadingDone = false;
        this.storage.remove('userDetailsDashboard');
        this.globalService.userDetails = null;
        this.navCtrl.navigateRoot(['login']);
        break;
      default:
        break;
    }
  }

  showHideMessagePanelClick() {
    this.bMessagePanelVisible = !this.bMessagePanelVisible;
  }

}
