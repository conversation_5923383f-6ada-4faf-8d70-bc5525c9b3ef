{"name": "lunchmate_kds", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "14.2.4", "@angular/common": "14.2.4", "@angular/core": "14.2.4", "@angular/forms": "14.2.4", "@angular/platform-browser": "14.2.4", "@angular/platform-browser-dynamic": "14.2.4", "@angular/router": "14.2.4", "@capacitor/android": "^6.2.0", "@capacitor/app": "^6.0.2", "@capacitor/cli": "^6.2.0", "@capacitor/core": "^6.2.0", "@capacitor/haptics": "^6.0.2", "@capacitor/ios": "^6.2.0", "@capacitor/keyboard": "^6.0.3", "@capacitor/network": "^6.0.3", "@capacitor/status-bar": "^6.0.2", "@ionic/angular": "^6.1.9", "@ionic/storage": "2.2.0", "@ng-idle/core": "^13.0.0", "@ng-idle/keepalive": "^13.0.0", "ionicons": "^6.0.3", "rxjs": "~6.6.0", "tslib": "^2.2.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "14.2.4", "@angular-eslint/builder": "~13.0.1", "@angular-eslint/eslint-plugin": "~13.0.1", "@angular-eslint/eslint-plugin-template": "~13.0.1", "@angular-eslint/template-parser": "~13.0.1", "@angular/cli": "14.2.4", "@angular/compiler": "14.2.4", "@angular/compiler-cli": "14.2.4", "@angular/language-service": "14.2.4", "@capacitor/assets": "^3.0.5", "@ionic/angular-toolkit": "^6.0.0", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.3.0", "@typescript-eslint/parser": "5.3.0", "eslint": "^7.6.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~3.8.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "typescript": "~4.7.3"}, "description": "An Ionic project"}